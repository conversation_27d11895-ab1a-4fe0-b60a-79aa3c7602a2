loyalApp_no: nie
loyalApp_yes: tak
actions.actions: Ak<PERSON><PERSON>
actions.actualize: Aktualizuj
actions.add: Dodaj
actions.add_attachment: Dodaj za<PERSON>
actions.add_notification: Dodaj powiadomienie
actions.add_client: Dodaj klienta
actions.add_code: Dodaj kody promocyjne
actions.add_key: Dodaj kartę
actions.add_multiple_key: Doładuj wiele kart
actions.add_package: Dodaj promocyjny pakiet
actions.add_user: Dodaj użytkownika
actions.cancel: Anuluj
actions.cancel_send: Anuluj wysyłanie
actions.chose: Wybierz
actions.clear: Wyczyść
actions.click_to_show_more_details: Kliknij by zob<PERSON><PERSON>ć szczegóły
actions.close: Zamknij
actions.delete: Usuń
actions.download-cards-usage-report: Pobierz raport użycia kart klienta
actions.edit: Edytuj
actions.edit_user: Edytuj użytkownika
actions.export: Eksport do
actions.export_csv: Eksport CSV
actions.export_pdf: Eksport PDF
actions.export_summary: Eksport podsumowania
actions.export_transactions: Eksport transakcji
actions.export_xlsx: Eksport XLSX
actions.generate: Generuj
actions.generate_and_download: Generuj i pobierz
actions.generate_invoice: Generuj fakturę
actions.invite_user: Zaproś użytkownika
actions.lock: Zablokuj
actions.postpone: Odłóż
actions.refill: Doładuj
actions.refill_and_issue_invoice: Doładuj i wystaw fakturę
actions.refill_card: Doładuj kartę
actions.return_to_list: Powrót do listy
actions.save: Zapisz
actions.save-and-add-other: Zapisz i dodaj następną
actions.send: Wyślij
actions.send_file: Prześlij plik
actions.send_invitation: Wyślij zaproszenie
actions.send_invoice: Wyślij fakturę
actions.service: Serwis
actions.show: Pokaż
actions.show-more-details: Zobacz więcej szczegółów
actions.show_doc: Przejdź do dokumentacji
actions.switch_user: Przełącz na użytkownika
actions.unlock: Odblokuj
actions.update: Aktualizuj
loyaltyCards_addedBy: Dodane przez
common_address: Adres
admin_allowSubscription: Abonamentowanie
admin_country: Kraj
admin_details: Szczegóły myjnii
admin_heading: Lista wszystkich myjni w CM
common_owner: Właściciel
admin_ownerBkf: Właściciel BKF
admin_product: Produkt
admin_serialNumber: Numer Seryjny
admin_startDate: Data startu
admin_warrantyVoided: Koniec gwarancji
common_dealer: Dealer
admin_subscribersDetails: Szczegóły subskrybenta
admin_detailsButton: Szczegóły
admin_subscribersHeading: Lista wszystkich subskrybentów
admin_isDealer: Jest dealerem
common_nip: Nip
admin_add: Dodaj abonament
admin_alreadyCancel: Abonament został już anulowany
admin_automaticPayment: Płatność automatyczna
admin_cancel: Anuluj abonament
admin_comment: Komentarz
admin_confirm: Potwierdź
admin_information: Informacja o abonamencie
admin_manualCancelNotPossible: Ręczne anulowanie niemożliwe
admin_save: Zapisz
administration.subscription.status.refund: Refundacja
administration.subscription.status.canceled: Anulowany
administration.subscription.status.error: Błąd
administration.subscription.status.initiated: Przetwarzany
administration.subscription.status.initiated_proforma: Zainicjowano proformę
administration.subscription.status.manually_canceled: Anulowany ręczenie
administration.subscription.status.paid: Opłacony
administration.subscription.status.unknown: Nieznany
admin_usersDetails: Szczegóły użytkownika
admin_usersHeading: Lista wszystkich użytkowników w CM
admin_isOwner: Jest właścicielem
admin_lastLogin: Ostatnie logowanie
common_usersOwner: Właściciel
admin_subscriptionCode: Aktywny abonament
admin_subscriptionReport: Raport subskrypcji
admin_subscriptionEnds: Koniec ostatniej subskrypcji
loyaltyCards_carwash: Myjnia
common_createDate: Data utworzenia alarmu
common_AlarmDuration: Czas trwania alarmu
common_timeDuration: Czas trwania
common_endDate: Data zakończenia alarmu
loyalApp_trustedPartnerWithoutFleetManager: Użytkownik nie będący użytkownikiem flotowym nie może być użytkownikiem zaufanym.
common_actionSucced: Sukces
service_sending: Wysyłanie...
loyaltyCards_topUpActionSucceed: Doładowanie aplikacji zakończone sukcesem
common_errorHeader: Błąd aplikacji
contact_message: Wystąpił błąd aplikacji. Skontaktuj się supportem by rozwiązać problem.
dashboard_average: Średnia
dashboard_max: Maximum
dashboard_min: Minimum
dashboard_standsTurnover: Obrót stanowisk
loyaltyCards_virtualCard: Karta wirtualna
loyaltyCards_activeTopups: Aktywne doładowania
loyaltyCards_clientLockWarning: Ręczne fakturowanie jest zablokowane. Aby je właczyć, zmień ustawienia fakturowania dla wybranego klienta.'
loyaltyCards_cyclicTopUpList: Aktywne doładowania cykliczne
loyaltyCards_notSentList: Doładowania oczekujące na wysłanie
common_cardActive: Karta aktywna
loyaltyCards_activeMessage: Kliknięcie przełącznika uniemożliwi użycie karty na myjni. Dostępne środków oraz historii użycia karty nie ulegną zmianie.'
common_cardBlocked: Karta zablokowana
loyaltyCards_blockedMessage: Kliknięcie przełącznika umożliwi ponowne użycie karty na myjni.'
loyaltyCards_card: Karta
loyaltyCards_cardFundsTooltip: Ostatnie informacje o środkach na karcie wysłane przez myjnie
common_cards: Karty
loyaltyCards_cardsList: Lista kart lojalnościowych
loyaltyCards_cleanHistoryAndBalance: czyści środki na karcie, oraz historię użycia.
loyaltyCards_foundsWaitingForSent: Środki oczekujące na wysłanie
loyaltyCards_invoiceLoading: Trwa generowanie faktury...
loyaltyCards_invoiceSendLoading: Wysyłanie faktury...
loyaltyCards_modifyTime: Data modyfikacji
loyaltyCards_name: Nazwa karty
loyaltyCards_number: Numer karty
loyaltyCards_removal: Usunięcie karty
loyaltyCards_source: Źródło
loyaltyCards_sourceStatus: Żródło / Status
loyalApp_topup: Doładowanie
common_topups: Doładowania
card.transactions: Transakcje kartą
card.transactions_list: Historia transakcji kartą lojalnościową
loyaltyCards_client: Klient
common_heading: Faktury
common_invoices_heading: Faktury
loyaltyCards_invoiceNumber: Numer faktury
loyaltyCards_issuanceDate: Data wystawienia
loyaltyCards_serviceDate: Data płatności
loyaltyCards_valueGross: Wartość brutto
loyaltyCards_valueNet: Wartość netto
carwash: Myjnia
common_rollover: Myjnia portalowa
common_selfService: Myjnia bezdotykowa
common_carwashSn: Numer seryjny myjni
finance_error: Błąd
loyalApp_qrCode: Kod QR
common_standCode: Kod stanowiska
finance_stands: Stanowiska
finance_topupDisabled: Doładowanie stanowiska dostępne w abonamencie premium na myjni obsługującej płatności mobilne.'
finance_topup: Doładowanie
finance_refillFor: Doładuj stanowisko o
finance_topupStand: Doładuj stanowisko
loyaltyCards_configuration: Konfiguracja
loyaltyCards_generateStrategy: Strategia generowania faktur
common_paymentMethod: Metoda płatności
common_cash: Gotówka
common_transfer: Przelew
client-modal.invoice.strategies.aggregated_month: Wystawiaj na koniec miesiąca
client-modal.invoice.strategies.auto-after-top-up: Wystawiaj po doładowaniu
client-modal.invoice.strategies.block: Nie wystawiaj
client-modal.invoice.strategies.manual: Wystawiaj na żądanie
client-modal.invoice.strategies.undefined: '-'
loyaltyCards_editClient: Edytuj klienta
loyaltyCards_heading: Lista klientów
loyaltyCards_invoicing: Fakturowanie
Czechy: Czechy
loyaltyCards_comment: Komentarz
loyaltyCards_discount: Zniżka
loyaltyCards_endTime: Zakończenie
common_cyclicTopUpsHeading: Doładowania Cykliczne
loyaltyCards_lastCal: Ostatnie wywołanie
loyaltyCards_startTime: Rozpoczęcie
loyaltyCards_active: Aktywne
loyaltyCards_state: Status
loyaltyCards_cyclicAdd: Cykliczne na kwotę
loyaltyCards_cyclicAlign: Cykliczne doładowujące do salda
loyaltyCards_oneTime: Jednorazowe
loyaltyCards_topUpType: Typ doładowania
loyaltyCards_type: Typ
loyaltyCards_value: Wartość
common_alarms: Alarmy
common_date: Data
dashboard.moneycollect.CAR_WASH: Stanowiska
dashboard.moneycollect.MONEY_CHANGER: Rozmieniarka
dashboard.moneycollect.YETI: YETI
dashboard.moneycollect.carwash: Myjnia
dashboard.moneycollect.title: Historia inkasacji
common_name: Nazwa
dashboard_noPermission: Brak przypisanych uprawnień. W celu przypisania skontaktuje się z administratorem serwisu.'
common_noAlarms: Brak alarmów
dashboard_header: Udział płatności
common_p14d: Ostatnie 14 dni
common_p7d: Ostatnie 7 dni
common_last: Od ostatniej inkasacji
common_now: Dzisiaj
dashboard_previousMonth: Poprzedni miesiąc
common_sinceMonthStart: Od początku miesiąca
common_yesterday: Wczoraj
common_software: Oprogramowanie
dashboard.subscription.component_header: Kończy się subskrypcja Carwash Manager
admin_dealer: Dealer
admin_subscription: Abonament
dashboard.subscription.subscriptionClick: Jeśli chcesz przedłużyć abonament kliknij przycisk poniżej.
dashboard.subscription.subscriptionEnd: Twoja subskrypcja aplikacji Carwash Manager wygasła
dashboard.subscription.subscriptionEndIn: 'Twoja subskrypcja aplikacji Carwash Manager kończy się:'
dashboard.subscription.subscriptionEndTitle: Subskrypcja wygasła
dashboard.subscription.subscriptionHelpText: 'W razie pytań prosimy o kontakt pod adresem e-mail:'
dashboard_sum: Suma
dashboard_summary: Razem
dashboard_yourCarwash: Twoje Myjnie
date.length.12m: 12 miesięcy
date.length.1m: 1 miesiąc
date.length.3m: 3 miesiące
date.length.6m: 6 miesięcy
common_selected: '{0} wybrano'
finance_0: Niedziela
finance_1: Poniedziałek
finance_2: Wtorek
finance_3: Środa
finance_4: Czwartek
finance_5: Piątek
finance_6: Sobota
user_deleteAccountInfo: W celu usunięcia konta w aplikacji prosimy o kontakt mailowy
service_attachment: Załącznik
service_description: Opis zgłoszenia
service_heading: Nowe zgłoszenie
service_attachments: Załączniki
service_close: Poproś o zamknięcie zgłoszenia
service_closedAt: Zamknięto
service_createdAt: Utworzono
service_eventsClose: Zamknięto zgłoszenie
service_readyToClose: Klient poprosił o zamknięcie zgłoszenia
service_start: Utworzono zgłoszenie
service_waitingForResponse: Oczekiwanie na odpowiedź klienta
service_listHeading: Zgłoszenia
service_status: Status
service_statusesClose: Zamknięte
service_new: Nowe
service_open: Otwarte
service_statusesReadyToClose: Gotowe
service_waiting: Oczekiwanie na reakcję
service_subject: Temat
service_user: Inicjator
service_replySent: Twoja odpowiedź została wysłana
service_replySentProblem: Podczas wysyłania odpowiedzi wystąpił problem
service_reportSent: Raport o błędzie wysłany
service_reportSentProblem: Nastąpił błąd przy wysyłce raportu
service_clientAdd: Problem z dodaniem klienta
service_financeData: Problem z danymi finansowymi
service_loyaltyCards: Problem z kartami lojalnościowymi
service_other: Inny problem niebędący usterką myjni
service_subscirption: Problem z Abonamentem
service_respond: Napisz swoją odpowiedź...'
service_errorReportSubject: Temat zgłoszenia
contact_title: Karta musi być aktywna by doładować jej saldo.
loyalApp_accessDenied: Nie masz uprawnień do wykonania tej operacji.
loyaltyCards_addExisting: Podana kara jest już zarejestowana w systemie.
loyaltyCards_invalidData: Niepoprawne dane karty
loyaltyCards_blockedTopup: Karta musi być aktywna jeżeli chcesz ją doładować.
loyalApp_problem: Error
loyalApp_userAlreadyExists: Użytkownik już istnieje. Nie można zaprosić istniejącego użytkowniak.
loyaltyCards_filtersActive: Aktywne
loyaltyCards_activity: Aktywność
service_allFeminine: Wszystkie
loyaltyCards_blocked: Zablokowane
loyaltyCards_cardType: Rodzaj karty
loyalApp_codeUsed: Wykorzystany kod
service_completed: Zakończone
loyalApp_ctime: Czas utworzenia
common_currentYear: Ten rok
common_custom: Dowolny
common_previousMonth: Poprzedni miesiąc
common_previousYear: Poprzedni rok
common_today: Dzisiaj
common_daterangeYesterday: Wczoraj
admin_deselectAll: Odznacz wszystko
loyaltyCards_funds: Środki
loyalApp_groupName: Nazwa grupy
common_inPeriod: W okresie
common_inPeriodCustom: Wybierz zakres dat
loyaltyCards_names: Nazwy
service_filtersOpen: Otwarte
loyaltyCards_regular: Fizyczna
admin_selectAll: Zaznacz wszystko
loyalApp_unusedCode: Niewykorzystany kod
loyaltyCards_usedInPeriod: Użyte w okresie
loyaltyCards_virtual: Wirtualna
loyaltyCards_withFounds: Ze środkami
loyaltyCards_withInvoice: Faktura wystawiona
loyaltyCards_withNames: Z nazwami
loyaltyCards_withoutFounds: Bez środków
loyaltyCards_withoutInvoice: Brak faktury
loyaltyCards_withoutNames: Bez nazw
fiscal_transactions.details.heading: Szczegóły transakcji
fiscal_transactions.export.name: fiscal_transactions
fiscal_transactions.for: za
fiscal_transactions.from: od
fiscal_transactions.heading: Transakcje fiskalne
fiscal_transactions.modal_add.heading: Dodaj transakcję fiskalną
fiscal_transactions.modal_add.isu: Kod ISU
fiscal_transactions.modal_add.location: Kod lokacji
fiscal_transactions.modal_add.password: Hasło certyfikatu
fiscal_transactions.source.CAR_WASH: Stanowisko
fiscal_transactions.source.DISTRIBUTOR: Dystrybutor
fiscal_transactions.source.INTERNET: Internet
fiscal_transactions.source.MONEY_CHANGER: Rozmieniarka
fiscal_transactions.source.SCRIPT: Serwer
fiscal_transactions.source.TERMINAL: Terminal
fiscal_transactions.source.UNKNOWN: Nieznane
fiscal_transactions.source.VACUUM_CLEANER: Odkurzacz
fiscal_transactions.table.carwash: Myjnia
fiscal_transactions.table.date: Data
fiscal_transactions.table.fiscal: Status
fiscal_transactions.table.net: Netto
fiscal_transactions.table.type: Typ transakcji
fiscal_transactions.table.value: Wartość
fiscal_transactions.table.vat: Vat
fiscal_transactions.table.fiscal_device: Urządzenie fiskalne
fiscal_transactions.to: do
fiscal_transactions.type.BANKCARDS: Płatność kartami bankowymi
fiscal_transactions.type.BANK_CARDS: Płatność kartami bankowymi
fiscal_transactions.type.CARWASH_MANAGER: Transakcja dodana ręcznie przez właściciela myjni
fiscal_transactions.type.CASH: Gotówkowa
fiscal_transactions.type.CASHLESS: Bezgotówkowa
fiscal_transactions.type.CHARGE_BAY: Doładowania stanowisk z rozmieniarki
fiscal_transactions.type.COINS: Płatność monetami
fiscal_transactions.type.HOPPER_A: Hopper A
fiscal_transactions.type.HOPPER_B: Hopper B
fiscal_transactions.type.LOYALTY_PAYING: Płatność kartą lojalnościową
fiscal_transactions.type.LOYALTY_PROMO: Promocyjne doładowanie karty lojalnościowej
fiscal_transactions.type.LOYALTY_RECHARGE: Doładowanie karty lojalnościowej
fiscal_transactions.type.LOYALTY_SELLING: Sprzedaż karty lojalnościowej
fiscal_transactions.type.MOBILE: Płatności mobilne
fiscal_transactions.type.NOTES: Płatność banknotami
fiscal_transactions.type.PROMO: Promocyjne doładowania stanowisk
fiscal_transactions.type.SERVICE: Impulsy serwisowe
fiscal_transactions.type.TOKENS: Płatność tokenami
fiscal_transactions.transactions: Transakcje
common_formAddress: Adres
common_city: Miejscowość
common_clientBasicData: Dane podstawowe
common_clientInvoiceData: Dane do wystawienia faktury
loyaltyCards_confirmCustomerData: Potwierdź dane do fakturowania kontrahenta
common_country: Państwo
loyaltyCards_currency: Waluta
common_fullCustomerName: Pełna nazwa kontrahenta
common_formName: Imię
common_postCode: Kod pocztowy
common_surname: Nazwisko
common_fieldRequired: Pole wymagane
form.validation.file_max_size_mb: Rozmiar pliku nie może przekraczać {size}MB
loyalApp_infoPositiveNumberOnly: Wartość powinna być dodatnia.'
loyaltyCards_infoValidKey: Dopuszczalne znaki to cyfry od "0" do "9" oraz litery od "a" do "f".'
loyaltyCards_infoValidKeyLength: Klucz BKF Key musi składać się z 8 znaków.'
loyalApp_infoValidNumberLength: Wartość nie może mieć wiecej niż 4 cyfr.'
loyalApp_infoValidNumberLength19: Wartość nie może mieć wiecej niż 19 cyfr.'
loyalApp_infoValidQuantity: Musisz użyć liczby z przedziału od "0" do "9".'
loyalApp_invalidValue: Niepoprawna wartość.
login_min: Wprowadzona wartość jest za krótka
login_passwordSame: Hasła muszą być identyczne
loyaltyCards_phoneNumber: Niepoprawny numer telefonu
loyaltyCards_topUpPositiveNumberOnly: Doładowanie musi mieć wartość dodatnią.'
loyaltyCards_topUpValueToBig: Wartość doładowania zbyt wysoka.'
loyalApp_validDescriptionLength: Opis powinien mieć maksymalnie 120 znaków.'
finance_noWhitespacesAtBeginEnd: Niedozwolone białe znaki na początku i końcu.
user_accountNumber: Numer rachunku bankowego (IBAN)
user_contactEmail: Email do kontaktu
user_contactEmailTooltip: Email do kontaku w stopce maili wysyłanych do klientów.
common_dataToInvoice: Dane do wystawienia faktury
user_editInvoiceDataAlert: 'W celu zmiany zablokowanych pól skontaktuj się z nami poprzez adres mailowy:'
common_emailCopyEmail: Adresy email kopii faktury
invoiceCompanyData_logo: Logo
common_invoiceCompanySettingsName: Pełna nazwa firmy
common_pressEnterToAddNew: Naciśnij klawisz Enter żeby dodać nowy adres
subscription_subscriptionBuyMissingData: 'W celu aktualizacji danych skontaktuj się z:'
common_taxNumber: Numer identyfikacji podatkowej
user_invoiceSettingsAccountNumber: Numer rachunku bankowego (IBAN)
loyaltyCards_additionalInfo: Dodatkowa informacja
loyaltyCards_clientCardSummaryReport: Wyślij na koniec miesiąca raport użycia kart
loyaltyCards_invoiceSettings: Ustawienia fakturowania
loyaltyCards_invoiceNumerator: Numerator faktury
common_logo: Logo
common_invoice_logo: Logo na fakturze
loyaltyCards_longMonthFormat: miesiąc w formacie 01 (dla stycznia)
loyaltyCards_longYearFormat: rok w formacie 2018
user_nextInvoiceNumber: Kolejny numer faktury
loyaltyCards_notOwnerAlert: Tylko właściciel myjni może zmieniać ustawienia fakturowania
common_paymentPeriod: Termin płatności
loyaltyCards_settings: Ustawienia
loyaltyCards_shortMonthFormat: miesiąc w formacie 1 (dla stycznia)
loyaltyCards_shortYearFormat: rok w formacie 18
loyaltyCards_vatTax: Stawka VAT
common_action: Akcja
admin_actionHistory: Historia akcji
common_administratorDetails: Dane administratorów
admin_alarmActive: Aktywne alarmy (ten problem)
admin_alarmActiveAllIssues: Aktywne alarmy (wszystkie problemy)
admin_alarmDetails: Szczegóły problemu
admin_alarmId: Identyfikator problemu
admin_alarmLevel: Poziom problemu
admin_allAlarmsForIssue: Nieaktywne alarmy (ten problem)
common_awc: AWC
common_awk: AWK
common_awp: AWP
common_carwashIp: IP
admin_carwashStatus: Status myjni
common_close: Zamknięte
admin_closeAllCarwashes: Zamknij wszystkie myjnie
predictiveMaintenance_comment: Komentarz
admin_contactDetails: Kontakty i notatki
common_description: Komentarz
admin_deselectAllIssues: Odznacz wszystkie problemy
admin_dueDate: Data odłożenia
common_duedate: Odłożone
common_employeesDetails: Dane pracowników
admin_etime: Data zamknięcia
common_issueHistory: Historia problemu
common_issueQuantity: Ilość problemów
admin_issuesList: Lista alarmów
common_jr: Wysłane do JR
common_lastAlarm: Aktualizacja alarmów
common_lastOnline: Płatności mobilne
common_lastSynchro: Aktualizacja obrotu
common_lastTime: Ostatnie wystąpienie
admin_lastUser: Ostatni użytkownik
common_lastAttendance: Ostatnie potwierdzenie obecności
admin_lastComment: Ostatni komentarz
common_lastVisit: Ostatnia zakończona wizyta
common_management: Zarządzanie złoszeniami
common_new: Nowe
common_nextVisit: Następna zaplanowana wizyta
common_none: Brak
common_noteDetails: Notaki urządzenia
common_open: Otwarte
admin_openAllCarwashes: Otwórz wszystkie myjnie
admin_otherIssues: Inne problemy
admin_pastIssues: Problemy zamknięte
common_postpone: Odłóż alarm do dnia
common_postponeForOneHour: Odłóż na 60 min.'
admin_previousIssue: Data zamkniecia poprzedniego problemu
common_priority: Priorytet zgłoszenia
common_quantity: Ilość aktywnych alarmów
common_refersTo: Dotyczy alarmów
admin_selectAllIssues: Zaznacz wszystkie problemy
common_status: Status problemu
common_loading: Wczytywanie...
contact_header: Wylogowany
contact_loggedOutMessage: Zostałeś wylogowany ponieważ twoja sesja wygasła. Zaloguj się ponownie.
subscription_logerPeriodBetterPrice: Dłuższy okres to lepsza cena
login_email: Adres e-mail
login_login: Zaloguj się
login_loginFailure: Logowanie nie powiodło się. Nieprawidłowy login lub hasło
login_password: Hasło
login_passwordForgot: Zapomniałem hasła
loyalApp_balanceFleetMemberNotify: Flota
loyalApp_balanceFleetMemberTooltip: Konto członka floty. Wszystkie operacje realizowanie są na saldzie konta właściciela floty
loyalApp_fleetManagerActive: Użytkownik jest menadżerem floty
loyalApp_fleetManagerActiveMessage: Kliknięcie przełącznika oznaczy użytkownika jako zwykłe konto.'
loyalApp_fleetManagerInactive: Użytkownik nie jest menadżerem floty
loyalApp_fleetManagerInactiveMessage: Kliknięcie przełącznika oznaczy użytkownika jako konto flotowe.'
common_invoices: Faktury
loyal-app-manager.no-invoice-data-message: Brak danych do faktury skontaktuj się z menadżerem floty w celu ich uzupełnienia.'
loyalApp_payments: Płatności
loyalApp_promotionalCodes: Kody promocyjne
loyalApp_promotionalPackages: Pakiety promocyjne
loyalApp_receipts: Paragony
loyalApp_regularUser: Zwykły użytkownik
loyalApp_selfInvoices: samofakturowanie
loyal-app-manager.transactions: Transakcje
loyalApp_trustedPartner: Zaufany partner
loyalApp_trustedPartnerActive: Użytkownik jest zaufanym partnerem
loyalApp_trustedPartnerActiveMessage: Kliknięcie przełącznika oznaczy użytkownika jako zwykłego. Doładowanie konta skarbonki bez dokonania natychmiastowej płatności będzie niemożliwe.'
loyalApp_trustedPartnerInactive: Użytkownik nie jest zaufanym partnerem
loyalApp_trustedPartnerInactiveMessage: Kliknięcie przełącznika oznaczy użytkownika jako zaufanego. Umożliwi to doładowanie salda przez użytkownika, bez dokonania natychmiastowej płatności. Faktura zostanie automatycznie wystawiona.'
loyalApp_client: Klient
loyalApp_info: Informacje
loyalApp_limit: Limity
loyalApp_stats: Statystyki
loyalApp_usersList: Lista użytkowników
loyalApp_usersListHeading: Lista użytkowników aplikacji lojalnościowej
loyalApp_confirmPaymentAndInvoie: Czy na pewno chcesz potwierdzić płatność ?'
dashboard_heading: Karty lojalnościowe
dashboard_value: Wartość
loyalsystem-widget.values-name.cardsTopUpSumFromCM: Suma doładowań i promocji z aplikacji CM
loyalsystem-widget.values-name.cardsTopUpSumFromCarwash: Suma doładowań i promocji z rozmieniarki
loyalsystem-widget.values-name.cardsUsedCount: Liczba użytych kart
loyalsystem-widget.values-name.transactionsPayments: Suma płatności
loyalsystem-widget.values-name.transactionsTopups: Suma doładowań
loyaltyCards_loyaltyTopupsHistoryHeading: Historia doładowań kart lojalnościowych
menu_administration: Administracja
common_administrationCarwashes: Myjnie
menu_administrationUsers: Użykownicy
common_alarmActive: Aktywne alarmy
common_alarmHistory: Historia alarmów
common_alarmManagement: Problemy na myjni
predictiveMaintenance_alarmsHistory: Archiwum problemów
menu_allSubscription: Subskrypcje
common_chart: Wykres danych parametrów
loyaltyCards_clients: Klienci
menu_cmdashboard: Pulpit
common_companyData: Dane firmy
menu_finance: Finanse
common_financeCarwashRates: Stawki na myjniach
menu.finance-fiscaltransactions: Transakcje fiskalne
finance_financeFiscalSummary: Podsumowanie transakcji fiskalnych
finance_summary: Podsumowanie
common_financeMobilePayments: Płatności mobilne
common_financeTurnover: Obrót
menu_logout: Wyloguj
common_loyalAppManager: White Label Application
common_loyalsystem: System lojalnościowy
common_moneycollect: Inkasacja
common_processData: Dane procesowe
menu_profile: Mój profil
common_service: Serwis
menu_subscribers: Subskrybenci
common_subscription: Abonament
menu_support: Kontakt
common_users: Użytkownicy
common_markRead: Oznacz jako przeczytaną
messages_message: Wiadomość
menu_more: Więcej
messages_noReadMessages: Brak przeczytanych wiadomości
common_noUnreadMessages: Brak nieprzeczytanych wiadomości
messages_read: Przeczytane
messages_unread: Nieprzeczytane
messages_when: Kiedy
common_by: Przez
common_clickToConfirm: Naciśnij żeby potwierdzić
common_confirm: Potwierdź
common_confirmHint: Czy na pewno chcesz potwierdzić fakturę ?
common_confirmation: Potwierdzenie
common_confirmed: Potwierdzona
common_mobilePaymentInvoicesDate: Data
common_download: Pobierz
mobile_payment_invoices.heading: Faktury
common_invoiceConfirmed: Faktura potwierdzona dnia
common_invoiceNumber: Numer faktury
loyalApp_issuerName: Nazwa sprzedawcy
loyalApp_issuerVatId: Vat sprzedawcy
common_notConfirmed: Niepotwierdzona
mobile_payment_invoices.not-for-app: 'Widok nie dostępny dla aplikacji:'
common_period: Okres
common_valueGross: Wartość brutto
mobile_payments.export.name: Płatności mobilne
finance_for: za
finance_from: od
finance_heading: Płatności mobilne
finance_carwash: Myjnia
finance_date: Data
finance_paymentType: Typ płatności
finance_standCode: Kod stanowiska
finance_total: Razem
common_totalOnPage: Razem na tej stronie
finance_value: Wartość
finance_to: do
loyaltyCards_cardNumber: Numer karty
loyaltyCards_createNew: Utwórz nową kartę lojalnościową
loyaltyCards_creationHint: W przypadku karty BKF Card i kluczy BKF Key które mają więcej niż 8 cyfr należy przepisać pierwszych 8 znaków alfanumerycznych
loyaltyCards_deleteHint: Uwaga, nie można ponownie dodać raz usuniętej karty.'
loyaltyCards_errorDescription: Opis błędu
loyaltyCards_errorDetails: Szczegóły
loyaltyCards_fileLine: Linia w pliku
loyaltyCards_lastFileUploadErrors: Błędy w przesłanym pliku
loyaltyCards_rechargeHint: "Każdy wiersz w przesłanym pliku musi mieć poniższy format: 'numer karty'; 'wartość doładowania'"
loyaltyCards_rechargeInfoHint: Prześlij plik CSV z doładowaniami kart lojalnościowych.'
loyaltyCards_rechargeMultiple: Doładuj wiele kart
loyaltyCards_hint: Usunięcie karty spowoduje, że zniknie ona z listy na ekranie kart lojalnościowych. Usuniętej karty nie można ponownie dodać do systemu.'
user_hint: Jesteś pewien, że chcesz usunąć tego użytkownika?
loyalApp_addCode: Utwórz nowe kody promocyjne
loyalApp_addPackage: Utwórz nowy pakiet promocyjny
loyalApp_editPackage: Edytuj pakiet
loyalApp_userEdit: Edycja użytkownika
loyaltyCards_emailInfo: Faktura zostanie wysłana na adres e-mail klienta
loyaltyCards_emailSend: Faktura zostanie wysłana na podany adres e-mail
modal.loyal_card_top_up_invoice.error.card_no_carwash: Nie można odnaleźć powiązanej myjni
modal.loyal_card_top_up_invoice.error.card_no_client: Karta nie ma przypisanego klienta
modal.loyal_card_top_up_invoice.error.invoice_exists: Faktura już istnieje w systemie
modal.loyal_card_top_up_invoice.error.invoice_generation_failed: Generowanie faktury nie powiodło się
modal.loyal_card_top_up_invoice.error.invoice_not_generated: Generowanie faktury nie powiodło się
modal.loyal_card_top_up_invoice.error.invoice_send_failed: Wysyłanie faktury nie powiodło się
modal.loyal_card_top_up_invoice.error.owner_no_country: Właściciel myjni nie ma ustawionego kraju
modal.loyal_notifications.creation_hint: Staraj się pisać krótkie teksty
modal.loyal_notifications.description: Tekst wysyłany do klientów
modal.loyal_notifications.new: Nowe powiadomienie
modal.loyal_notifications.preview: Podgląd powiadomienia
modal.loyal_notifications.title: Tytuł
moneycollect.devices.CAR_WASH: Stanowisko
moneycollect.devices.DISTRIBUTOR: Dystrybutor
moneycollect.devices.VACUUM_CLEANER: Odkurzacz
finance_exchangerMoneyCollections: Inkasacja rozmieniarki
moneycollect.export.name: moneycollect
finance_moneycollectFor: za
finance_moneycollectFrom: od
finance_standMoneyCollections: Inkasacja stanowisk
finance_balance: Bilans
finance_bankCards: Wpłacono kartą
finance_details: Szczegóły inkasacji
finance_emergencyDrops: Zrzuty awaryjne
finance_safe: Sejf
finance_sorted: Posortowane
finance_sucked: Pobrane
finance_sumCash: Wpłacono gotówką
finance_sumHoppers: Wydano (hoppery)
finance_sumSale: Sprzedaż
finance_sumSorted: Posortowane do rozmieniarki i sejfu
finance_sumSucked: Pobrane z myjni
finance_units: szt.'
finance_unrecognized: Nierozpoznane
finance_yeti: Szczegóły inkasacji YETI
finance_moneycollectTo: do
finance_yetiMoneyCollections: Inkasacja YETI
name: Nazwa
subscription_noCarwashAttached: Brak myjni przypisanych do użytkownika
subscription_alertContent: Dane do wystawienia faktury nie zostłay uzupełnione, proszę uzupełnij je.
number: Numer
user_others: Inne
common_0: 0 dni
common_1: 1 dzień
common_10: 10 dni
common_14: 14 dni
common_3: 3 dni
loyalApp_30: 30 dni
common_5: 5 dni
common_7: 7 dni
subscription_subscription: Płatność za abonament
loyalApp_value: wartość płatności
processData_chart: Wykres
processData_parameter: Parametr
common_updateTime: Ostatnia aktualizacja
processData_value: Wartość
common_itemOld: Wartość może być nieaktualna
user_account: Konto użytkownika
user_baseData: Dane podstawowe
user_notificationNotice: Funkcja powiadomień dostępna w pakiecie Podstawowym
user_notificationsSettings: Ustawienia powiadomień
user_moneyCollectEmail: Wysyłaj powiadomienia o inkasacjach na adres email
user_sendAlarmNotificationEmail: Powiadomienia o alarmach na adres email
user_sendAlarmNotificationMobile: Powiadomienia o alarmach do aplikacji mobilnej
user_regionalSettings: Ustawienia regionalne
user_reportsAndNotifications: Raporty i powiadomienia
profile-configuration.reports-notice: Funkcja raportów email dostępna w pakiecie Podstawowym
profile-configuration.reports-settings: Ustawienia raportów
profile-configuration.reports.fiscal-transactions: Raport transakcji fiskalnych
profile-configuration.reports.mobile-payments: Raport płatności mobilnych
profile-configuration.reports.program-usage: Raport użycia programów
profile-configuration.reports.used-funds: Raport wykorzystania środków
user_error: wysyłaj od poziomu "Błąd" włącznie'
user_info: wysyłaj od poziomu "Informacja" włącznie'
user_warning: wysyłaj od poziomu "Ostrzeżenie" włącznie'
user_dontSend: nie wysyłaj
profile-configuration.sending-options.periodic.daily: wysyłaj codziennie
profile-configuration.sending-options.periodic.monthly: wysyłaj co miesiąc
profile-configuration.sending-options.periodic.weekly: wysyłaj co tydzień
user_send: wysyłaj
user_siteTitle: Mój profil
finance_carwashUsage: Użycie myjni bezdotykowych
programsusage.daily: Użycie dzienne
finance_programsusageFor: za
finance_programsusageFrom: od
common_title: Rozkład godzinowy użycia
finance_overTimeTitle: Użycie programów w czasie
finance_title: Procentowy udział programów
finance_programs: Użycie programów
finance_rolloverUsage: Użycie myjni portalowych
programsusage.rollover_total: Razem
programsusage.table.sum: Suma
programsusage.table.wash: x mycie
programsusage.table.water-average: Średnie zuż. wody
programsusage.table.water-usage: Całkowite zuż. wody
programsusage.table.availability: Availability
finance_programsusageTo: do
common_total: Użycie
loyalApp_quantity: Ilość
loyalApp_packageValue: Wartość pakietu
finance_mtime: Data modyfikacji
finance_name: Nazwa myjni
finance_pause: Wstrzymane
finance_time: Widok stawek w sekundach/impuls
finance_valueForCredit: Pokaż w
loyalApp_createDate: Data wystawienia
loyalApp_number: Numer
loyalApp_paymentDate: Data płatności
loyalApp_loyaltyAppUserTopups: Historia użytkownika aplikacji lojalnościowej
loyalApp_showAppUsageHistory: Wyświetl historię użycia aplikacji lojalnościowej
login_backToLogin: Powrót do logowania
login_forgotSuccess: Na podany adres e-mail wysłaliśmy link do resetowania hasła
login_reset: Zresetuj hasło
login_resettingBackToLogin: Powrót do logowania
login_changePassword: Zmień hasło
login_confirmPassword: Powtórz hasło
login_changeSuccess: Sukces! Hasło zostało zmienione
login_tokenExpired: Link do resetowania hasła wygasł
login_newPassword: Nowe hasło
service_button: Zgłoś usterkę
service_text: Aby utworzyć zgłoszenie wejdź na stronę
service_serviceHeading: Zgłoszenia serwisowe
service.status.desc.completed: Zakończone
service.status.desc.open: Otwarte
common_carwash: Myjnia
admin_issueId: ID
service_issueReportSource: Źródło
service_issueReportedBy: Zgłaszający
service_issueTitle: Temat
service_noContent: Brak treści
service_tableStatus: Status
service_time: Data zgłoszenia
subscription_state: status
admin_subscriptionListHeading: Lista wszystkich subskrypcji
subscription.carwash-type.STANDARD: Standard
subscription.carwash-type.UNSUBSCRIBED: Bez subskrypcji
subscription.carwash-type.WARRANTY: Gwarancja
admin_subscriptionDealer: Dealer
common_document: Dokument
admin_dontIssue: Nie wystawiaj
admin_issue: Wystaw
admin_issueAndSend: Wystaw i wyślij
admin_subscriber: Subskrybent
subscription.table.STANDARD: Myjnie pogwarancyjne
subscription.table.UNSUBSCRIBED: Myjnie bez subskrypcji
subscription.table.WARRANTY: Myjnie gwarancyjne
subscription.table.discount: Rabat
subscription.table.position: Pozycja
subscription.table.price-after-discount: Cena po rabacie
subscription.table.price-before-discount: Cena przed rabatem
subscription.table.sum: Razem
subscription.table.summary: Podsumowanie
subscription.table.type: Rodzaj
admin_whoPays: Kto płaci ?
subscriptions.actions.chose-and-pay: Wybierz i opłać abonament
subscription_back: wróć
common_canceled: Anulowana
subscription_chose: Wybór abonamentu
subscription_chosePaymentPeriod: Wybierz okres płatności
subscription_chose2: Wybierz abonament
subscriptions.delete-question: Czy chcecsz anulować wybrany abonament ?'
admin_clientDiscount: zniżka klienta
common_subscriptionsEnddate: Do
subscription_goToDataCompletion: Przejdź do uzupełnienia danych
subscription_historyList: historia rozliczeń
subscription_alarmsList: Lista alarmów - aktywnych i historycznych'
subscription_cyclicFiscalMailReports: Cykliczne mailowe raporty finansowe
subscription_loyalcardsCyclicTopup: Cykliczne doładowania kart lojalnościowych
subscription_loyalcardsInvoicing: Fakturowanie kart lojalnościowych
subscription_loyalcardsRemoteTopups: Zdalne doładowanie stanowisk mycia
subscription_loyalcardsReport: Zestawienia użycia kart lojalnościowych dla klientów końcowych
subscription_loyalcardsTransactionHistory: Historia transakcji na myjni wraz ze szczegółowymi informacjami fiskalnymi
subscriptions.options.loyalcards-transactions-history: CRM - Historia transakcji, zdalne doładowania, blokady kart lojalnościowych
subscription_mailReports: Raporty mailowe
subscription_mobileAppAccess: Dostęp przez aplikację mobilną
subscription_moneycollectControll: Kontrola inkasacji gotówki z myjni (raporty stanu gotówki)
subscription_newAlarmNotifications: Powiadomienia o nowych alarmach na myjni (e-mail, push)
subscription_serviceTicketPreview: Podgląd stanu zgłoszeń serwisowych
subscription_technicalData: Podgląd aktualnych parametrów pracy myjni
subscription_unlimitedUsers: Nielimitowani użytkownicy i przydzielanie indywidualnych uprawnień
subscription_usageStatistics: Statystyki użycia programów i myjni
subscription_wwwAccess: Dostęp przez przeglądarkę internetową
common_error: Błąd
common_paid: Opłacono
subscription_paymentSummary: Podsumowanie płatności
common_price: Cena brutto
subscription_priceForCarwashForMonth: Cena za 1 miesiąc (cena netto) za 1 myjnie
common_processing: Przetwarzanie
common_refund: Zwrot
common_startDate: Od
subscription_checkInvoiceData: Zweryfikuj dane do faktury
subscription_goToAbonamentLength: Przejdź do wyboru okresu abonamentowania
subscription_orderWithPaymentObligation: Zamawiam z obowiązkiem zapłaty
subscriptions.subscription: Abonament
subscription_subscriptionContent: Zawartość abonamentu
subscription_summary: Razem
common_timeout: Przekroczony czas
subscription_toPay: do zaplaty
subscriptions.types.basic: Podstawowy
subscriptions.types.free: Darmowy
subscriptions.types.premium: Premium
common_unknown: Nieznany
admin_vat: Vat
subscription_yourSubscription: Twój abonament
common_success: Sukces
loyalApp_account: Konto
loyalApp_accountType: Typ rachunku
admin_added: Dodano
common_alarmDetails: Szczegóły alarmu
common_alarmId: Identyfikator alarmu
common_alarmLevel: Poziom alarmu
common_all: Wszystkie
loyalApp_averagePaymentValue: Średnia wartość płatności
loyalApp_balance: Saldo
loyaltyCards_cardFunds: Środki na karcie
common_carwashes: Myjnie
common_client: Klient
common_comment: Komentarz
table.company_name: Nazwa firmy
common_tableCreateDate: Data utworzenia
common_currency: Waluta
loyaltyCards_cyclic: Cykliczne
common_dataUnknown: brak danych
common_tableDate: Data
common_tableDescription: Opis
common_discount: Zniżka
common_email: Adres email
loyalApp_externalId: Zewnętrzny identyfikator
loyalApp_externalType: Typ wpłaty
loyalApp_externalValue: Wartość wpłaty
admin_filters: Filtry
common_firstName: Imię
loyalApp_fleetManager: Menadżer floty
common_id: identyfikator
common_invoice: Faktura
loyalApp_invoiceNumber: Numer faktury
loyalApp_issuanceDate: Data wystawienia
common_language: Język
common_lastLogin: Ostatnie logowanie
dashboard_lastActualizaction: Ostatnia aktualizacja
common_lastName: Nazwisko
common_lastUsage: Ostatnie użycie
loyalApp_licensePlate: Numery rejestracyjne
loyalApp_managerEmail: Adres menadżera floty
common_noData: Brak danych do wyświetlenia
common_notFullySent: Nie w pełni wysłane
admin_tableOwnerBkf: Id Właściciela BKF
loyalApp_payment: Płatność
loyalApp_paymentCount: Liczba płatności
common_paymentDate: Data płatności
loyaltyCards_payments: Płatności
common_phone: Telefon
loyalApp_promotionalCode: Kod promocyjny
common_receipt: Paragon
common_regonNumber: Regon
common_roles: Role
common_rowsPerPage: 'Wierszy na stronę:'
loyalApp_salesDocument: Dokument sprzedaży
common_search: Szukaj
common_sendToCard: Wysłane
common_stand: Stanowisko
common_state: Status
loyaltyCards_sum: suma
loyalApp_sumPaymentValue: Całkowita wartość płatności
table.tax_number: Nip
loyalApp_taxNumber: Nip
common_timezone: Strefa czasowa
loyalApp_title: Tytuł
common_toSent: Oczekujące
loyaltyCards_toSend: do wysłania
loyaltyCards_topUpsToSent: Doładowania oczekujące na wysłanie
loyaltyCards_topup: Wartość doładowania
loyalApp_topupBonus: Promocja
common_topupCode: Kod promocyjny
common_topupSent: Doładowania wysłane
common_transactionType: Rodzaj transakcji
loyalApp_transactionValue: Wartość transakcji
loyalApp_tableTrustedPartner: Zaufany partner
common_type: Typ
common_user: Użytkownik
common_username: Nazwa użytkownika
common_value: Wartość
loyaltyCards_valueAfterTransaction: Saldo po transakcji
loyalApp_vat: Vat
loyaltyCards_tableVirtualCard: Karta wirtualna
admin_whoAdded: Dodano przez
common_accept: Akceptuję
common_cancel: Anuluj
common_termsHeading: REGULAMIN APLIKACJI CAR WASH MANAGER
common_terms: Regulamin
common_validFrom: obowiązujące od dnia 25 maja 2020 r.'
finance_minutes: min
transactions.balance_adjustment: Wyrównanie
transactions.car_wash: Z myjni
transactions.distributor: Dystrybutor
transactions.export_error: Problem z wygenerowaniem raportu
transactions.history: Historia transakcji
transactions.history_for_card: Historia transakcji kartą
transactions.internet: Internetowe
transactions.money_changer: Z rozmieniarki
transactions.payment: Płatność
transactions.payments: Płatności
transactions.promotions: Promocje
transactions_refill_to_balance: Saldo po doładowaniu
transactions.refill_for: Doładuj kartę o
transactions.top-up-code: Kod promocyjny
transactions.topup: Doładowanie
transactions.topup_card_history: Historia doładowań karty
transactions.topup_history: Historia doładowań
transactions.topups: Doładowania
transactions.unknown: Nieznane
transactions.vacuum_cleaner: Odkurzacz
turnover.devices.distributor.plural: Dystrybutory
turnover.devices.distributor.singular: Dystrybutor
turnover.devices.stand.plural: Stanowiska
turnover.devices.stand.singular: Stanowisko
turnover.devices.vacuum.plural: Odkurzacze
turnover.devices.vacuum.singular: Odkurzacz
finance_exportName: turnover
common_filtersCarwash: Myjnia
common_deviceType: Typ urządzenia
finance_turnoverFor: za
finance_turnoverFrom: od
finance_fromLastCollection: z ostatniej inkasacji
finance_compareWithPreviousYear: Porównaj z poprzednim rokiem
finance_linechartTitle: Obrót w funkcji czasu
finance_ofAllCarwashes: wszystkich myjni
finance_ofCarwash: 'myjni #'
finance_paymenttypesharepieName: Udział typów płatności
common_paymenttypesharepieTitle: Udział typów płatności
turnover.table.balance: Bilans
turnover.table.bankCards: Karty bankowe
turnover.table.banknotes: Banknoty
turnover.table.bkfCardPay: Płatność BKF Card
turnover.table.bkfCardSale: Sprzedaż BKF Card
turnover.table.bkfKey: BKF Card
turnover.table.bkfKeyRecharge: Doładowania BKF Card
turnover.table.carwash: Myjnia
turnover.table.carwashEarnings: Suma
turnover.table.carwashRecharge: Doładowania stanowisk
turnover.table.cash: Płatności gotówkowe
turnover.table.cashless: Płatności bezgotówkowe
turnover.table.clients: Klienci
turnover.table.coins: Monety
turnover.table.counter: Liczba użyć
turnover.table.date: Data
turnover.table.details: Szczegóły myjni
turnover.table.detailsRollover: Szczegóły myjni portalowej
turnover.table.exchanger: Szczegóły rozmieniarki
turnover.table.exchangerHoppers: Rozmiany
turnover.table.exchangerSale: Sprzedaż rozmieniarki
turnover.table.exchanges: Wydano
turnover.table.hopperA: Hopper A
turnover.table.hopperB: Hopper B
turnover.table.mobilePayments: Mobilne
turnover.table.name: Urządzenie
turnover.table.paid: Wpłacono
turnover.table.post: Doładowania z rozmieniarki
turnover.table.prepaid: Przedpłaty
turnover.table.programsSale: Sprzedaż programów
turnover.table.promotion: Promocja
turnover.table.saleValue: Wartość sprzedaży
turnover.table.sell: Sprzedano
turnover.table.service: Serwis
turnover.table.sum: Suma
turnover.table.terminal: Szczegóły terminala płatniczego
turnover.table.tokens: Żetony
turnover.table.total: Razem
common_daily: Dzienny
finance_detailed: Szczegółowy
common_monthly: Miesięczny
finance_tabsTotal: Całkowity
finance_turnoverTitle: Obrót
finance_turnoverTo: do
finance_turnover: obrót
unknown: Nieznany
finance_cubicMeters: m³
finance_litres: l
loyalApp_discount: Zniżka
loyalApp_max: Maximum
loyalApp_min: Minimum
loyalApp_term: Termin
common_userEmailAlreadyExist: Użytkownik o podanym adresie email już istnieje w naszej bazie.
loyalApp_invitationInfo: Zaproszony użytkownik będzie mógł korzystać ze środków konta menadżera floty.
subscription_whyThisPrice: Skąd ta cena?
subscription_whyThisPriceModalHint: Poniższa tabela przestawia wyliczenie kwoty abonamentu na podstawie posiadanych myjni i przyznanych zniżek.
loyalApp_topupAccountTitle: Doładuj konto użytkownika
loyalApp_topupType: Typ doładowania
loyalApp_bonus: Bonus
loyalApp_invoice: Faktura
loyalApp_topupByAmount: Doładuj konto o kwotę
loyalApp_topupAccountTopup: Doładuj
loyalApp_loyalappExpandDetailsToSeeAlerts: Rozwiń szczegóły aby zobaczyć komunikaty użytkownika.
loyalApp_loyalappUserAlert: Alerty
common_exportAsyncTitle: Generuj raport
common_userTitle: Tytuł raportu
common_generating: Generowanie Raportu
common_export: Eksport
common_downloadReport: Pobierz raport
common_reportReady: Raport gotowy do pobrania
common_clearReport: Wyczyść
common_canotGenerateReport: Nie udało się wygenerować raportu
actions.export_csv_summary: Eksport podsumowania CSV
actions.export_xlsx_summary: Eksport podsumowania XLSX
actions.export_pdf_summary: Eksport podsumowania PDF
actions.chose_report_to_generate: Wybierze raport do wygenerowania
common_reports: Raporty
common_reportDownloadOnList: Raport do pobrania na liście raportów
finance_header: Lista wygenerowanych raportów
finance_createTime: Data utworzenia
finance_endTime: Data wygenerowania
finance_status: Status
finance_reportName: Nazwa raportu
finance_user: Użytkownik
common_progressNew: Nowy
common_process: Przetwarzanie
common_done: Gotowy
common_progressError: Błąd
reports_list.progress.QUEUE: Zakolejkowane
finance.fiscal-transactions: Transakcje fiskalne
card_client_report.card_report_email_header: Raport użycia kart klienta
common_turnoverFrom: Obrót środkami na myjni w okresie
card_client_report.cards_transactions: Transakcje kartami
common_mobilePayments: Płatności mobilne
common_programUsage: Użycie programów
fiscal.source.CAR_WASH: Stanowisko
fiscal.source.DISTRIBUTOR: Dystrybutor
fiscal.source.MONEY_CHANGER: Rozmieniarka
fiscal.source.UNKNOWN: Nieznane
fiscal.source.VACUUM_CLEANER: Odkurzacz
finance_success: Sukces
finance_initiated: Zainicjowano
finance_refused: Odrzucono
dashboard.table-details: Szczegóły
invoices.send-date: Data wysłania
programs.brush: Szczotka
programs.degreaser: Odtłuszczacz
programs.foam: Piana
programs.glossing: Nabłyszczanie
programs.mainwash: Mycie zasadnicze
programs.prewash: Oprysk wstępny
programs.rims: Oprysk felg
programs.rinsing: Spłukiwanie
programs.wasxing: Woskowanie
table.serialnumber: Numer seryjny
fiscal_transactions.grouped: Podsumowanie dzienne
loyalApp_showOnMap: Pokaż na mapie
loyalApp_paymentEnabled: Płatności aktywane
loyalApp_paymentNotEnabled: Płatności nie aktywane
loyalApp_productionCarwash: Myjnia produkcyjna
loyalApp_testCarwash: Myjnia testowa
carwashes_list.last_connection: Ostatnie połączenie
loyalApp_mobileOk: Płatności działają
finance_periodError: Rarport nie obsługuje wybranego zakresu dat. Wybierz inny okres by wygenerować raport.
other_lastOnline: Ostatnio online
loyalApp_online: Myjnie online
loyalApp_offline: Myjnie offline
loyalApp_invoicedAfterTransaction: Chcę otrzymywać automatycznie fakturę na adres email
common_cyclic: Cykliczny
common_onetime: Jednorazowy
common_typeType: Typ raprotu
common_frequency: Częstotliwość
common_frequency_daily: Codziennie
common_frequency_weekly: Co tydzień
common_frequency_monthly: Co miesiąc
finance_listHeading: Raporty cykliczne
finance_extension: Rozszerzenie
thisWeek: Ten tydzień
lastWeek: Poprzedni tydzień
discountPercentage: Rabat (%)
discountValue: Wartość rabatu
yes: Tak
no: Nie
amountToPay: Do zapłaty
downloadInvoice: Pobierz fakturę
loyalApp_valid_time: Czas Ważności (dni)
loyalApp_invoiceStatus: Status Faktury
mobileAppVersion: Wersja aplikacji mobilnej
fiscal_transactions.lastReceipt: Ostatni paragon
fiscal_transactions.fiscalDevicesDetails: Szczegóły kas fiskalnych
admin_subscribentReports: Raporty subskrybenta
admin_fiscalization: Fiskalizacja
finance_dosage: Dozowanie
finance_carwashRates_synchronization: Synchronizacja
common_refund_question: Czy chcesz zwrócić wybraną płatność ?
commonAction_refund: Zwróć
common_viewRelocatedTo: Widok przeniesiony na stronę
common_goToSite: Przejdź
invoiceConfig_department_identificator: Identyfikator departamentu
invoiceConfig_category: Kategoria
invoiceConfig_token: Klucz api
invoiceConfig_url: Adres url
invoiceConfigType_Disabled: Wyłączone
invoiceConfigType_Internal: Carwash Manager
invoiceConfigType_Fakturownia: fakturownia.pl
invoiceConfigType: Fakturowanie
financeRollover_programHistory: Historia myć myjni portalowej
financeRollover_rides: Przejazdy
financeRollover_step: Krok
financeRollover_duration: Czas trwania
financeRollover_programName: Nazwa programu
financeRollover_status: Status
common_reset: Reset
cyclicReport_deleteQuestion: Czy na pewno chcesz usunąć raport cykliczny ?
common_accountDelete: Usunięcie konta
loyalty_PaymentReportTitle: Raport płatności zewnętrznych dla kart
common_initiated: Zapoczątkowana
common_waiting: W oczekiwaniu
common_pending: W trakcie
common_rejected: Odrzucona
loyaltyCards_paymentSettings: Ustawienia płatności
loyaltyApp_startTime: Data rozpoczęcia
loyaltyApp_endTime: Data zakończenia
common_startEndDateValidationInfo: Data zakończenia musi być późniejsza niż data rozpoczęcia
client-modal.clientAlerts.editRequired: Wymaga edycji
loyaltyCards_lockFund: Ściągnięcie środków po blokadzie karty
wla_user_card_name: Nazwa
wla_user_card_balance: Saldo
wla_user_card_end_time: Ważność
wla_user_card_list_header: Karty klienta
loyalApp_Reports: Raporty
loyalApp_CarwashStats: Statystyki myjni
common_actions: Akcje
loyalSystem_packages: Pakiety
validation_discount: Wartość zniżki przyjmuje wrtości od 0 do 100.
validation_integer: Wartość musi być liczbą całkowitą
loyalApp_stands: Stanowiska
loyalApp_cashback: Cashback
loyalApp_carwashBasicData: Dane podstawowe
loyalApp_subscriptions: Subskrypcje
loyalApp_user: Użytkownik
loyalApp_fleet: Flota
loyalApp_usage: Zużycie
loyalApp_subscriptionsPackagesList: Lista subskrypcji aplikacji lojalnościowej
loyalApp_carwash_photo: Zdjęcie myjni
common_left: Pozostało
fiscal_transactions_configDetails: Certyfikat kasy fiskalnej
fiscal_transactions_config_oib: OIB
fiscal_transactions_config_errorCounter: Licznik błędów
fiscal_transactions_config_expiration: Data wygaśnięcia
fiscal_transactions_config_validFrom: Ważne od
fiscal_transactions_config_subject: Podmiot
fiscal_transactions_config_error: Błąd podczas pobierania danych certyfikatu
fiscal_transactions_config_noData: Brak certyfikatu dla tego urządzenia
fiscal_transactions_certificate_uploadBtn: Wgraj certyfikat
fiscal_transactions_certificate_file: Plik certyfikatu (.p12)
fiscal_transactions_certificate_oibFormat: OIB powinien składać się z 11 cyfr
fiscal_transactions_certificate_fileFormat: '"Dozwolony format pliku: .p12"'
fiscal_transactions_certificate_error: Wystąpił błąd podczas wgrywania certyfikatu
common_upload: Wgraj
common_select_file: Wybierz plik
carwash_software_info: Informacje o oprogramowaniu
carwash_software_plc: MAC
carwash_software_mac: PLC
carwash_software_serial_number: Numer seryjny
carwash_software_owner: Właściciel
carwash_software_ip: IP
carwash_software_config: Konfiguracja
carwash_software_current_version: Aktualna wersja
carwash_software_target_version: Docelowa wersja
carwash_software_ip_country: Kraj IP
carwash_software_ip_city: Miasto IP
carwash_software_start_date: Data uruchomienia
carwash_software_warranty_to: Gwarancja do
carwash_software_no_data: Brak danych o oprogramowaniu
carwash_software_target_software_version: Docelowa wersja oprogramowania
carwash_software_select_version: Wybierz wersję oprogramowania
carwash_software_loading_versions: Ładowanie listy wersji...
carwash_software_no_versions: Brak dostępnych wersji
common_set: Ustaw
common_error_occurred: Wystąpił błąd
loyalApp_statistics_heading: Statystyki aplikacji lojalnościowej
loyalApp_statistics_chart_payments_users: Płatności i użytkownicy
loyalApp_statistics_chart_by_source: Wartość skarbonek
loyalApp_statistics_users_previous_year: Użytkownicy (rok poprzedni)
loyalApp_statistics_table_heading: Płatności, użytkownicy i saldo
common_monetary_values: Wartości pieniężne
SUBSCRIPTION: Subskrypcja
BKFPAY: BKFPay
loyalApp_directPayments: Bezpośrednie płatności
loyalApp_promotional_package: Pakiet promocyjny
