<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        lg="12"
        xl="12"
        class="px-0"
      >
        <v-layout
          row
          wrap
        >
          <v-col
            md="8"
            sm="12"
            cols="12"
          >
            <v-layout
              row
              justify-start
              wrap
            >
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <text-search
                  v-model="filtering.search.text"
                  :disabled="loader"
                  @input="getDataDebounced"
                />
              </v-col>
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <multiselect
                  v-model="status"
                  :items="statusesDisabled"
                  :label="filters.statuses.text"
                  :prepend-icon="filters.statuses.icon"
                  :return-array="true"
                  :disabled="loader"
                  unified
                  dense
                  allow-null
                />
              </v-col>
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <multiselect
                  v-model="issuers"
                  :items="issuerItems"
                  :label="filters.issuers.text"
                  :prepend-icon="filters.issuers.icon"
                  :disabled="loader"
                  dense
                  unified
                  allow-null
                />
              </v-col>
            </v-layout>
          </v-col>
          <v-col
            md="4"
            sm="12"
            cols="12"
          >
            <date-range-picker
              key="dateRangeLoyalAppUsage"
              ref="dateRangeLoyalAppUsage"
              :show-presets="true"
              start-preset="currentMonth"
              @reload-transaction-list="onDateRangeChange"
            />
          </v-col>
        </v-layout>
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="12"
            class="pt-1 pb-1 pr-1 pl-1"
          >
            <v-layout
              row
              wrap
            >
              <v-col
                cols="12"
                sm="12"
                class="display-flex align-center pt-1 pb-1 px-3"
              >
                <v-layout
                  justify-end
                  align-center
                >
                  <btn-refresh
                    class="mt-0"
                    size="small"
                    :disabled="loader"
                    @click="getData"
                  />
                  <report-create-modal
                    btn-class="ml-2"
                    :params="exportAsyncParams"
                    :disabled="loader"
                    :preset="filtering.search.dateValue"
                  />
                </v-layout>
              </v-col>
            </v-layout>
          </v-col>
        </v-layout>
        <v-layout
          row
          wrap
        />
      </v-col>
      <v-col cols="12">
        <div
          v-if="loader"
          class="loader-background"
        />
        <v-data-table
          v-resize="onResize"
          mobile-breakpoint="0"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :loading="loader"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
          :footer-props="filtering.footerProps"
        >
          <template #progress>
            <div class="text-center">
              <v-progress-circular
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <template #item="{ item }">
            <template v-if="!loader">
              <tr>
                <td class="text-sm-start">
                  <div class="flex-inline-start">
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-icon
                          :color="getStatusDetails(item.status).color"
                          v-bind="attrs"
                          v-on="on"
                        >
                          {{ getStatusDetails(item.status).icon }}
                        </v-icon>
                      </template>
                      <span> {{ getStatusDetails(item.status).text }}</span>
                    </v-tooltip>
                    {{ item.id }}
                  </div>
                </td>
                <td class="text-start border-right text-no-wrap">
                  <template v-if="null !== item.time">
                    {{ getDateInUTC(item.time) }}
                  </template>
                </td>
                <td class="text-start">
                  {{ item.user_email }}
                </td>
                <td class="text-start">
                  {{ item.client_name ?? '-' }}
                </td>
                <td class="text-end">
                  {{ item.client_tax_number ?? '-' }}
                </td>
                <td class="text-start">
                  {{ item.issuer }}
                </td>
                <td class="text-end">
                  {{ item.external_id ?? '-' }}
                </td>
                <td class="text-end hidden-sm-and-down md-and-up border-right">
                  {{
                    item.value|currencySymbol(item.currency_symbol)
                  }}
                </td>
                <td class="text-end text-no-wrap">
                  <div
                    class="flex-inline-end"
                  >
                    <template v-if="item.document_id !== null">
                      {{ item.document_number }}
                      <v-icon>
                        {{ getDocumentTypeIcon(item.document_type) }}
                      </v-icon>
                      <act-download
                        v-if="item.document_uri"
                        :url="`/api/gateway/wla-admin${item.document_uri}?app=${app}`"
                      />
                    </template>
                    <template
                      v-else
                    >
                      -
                    </template>
                  </div>
                </td>
                <td>
                  <refund-dialog
                    :app="app"
                    :payment-id="item.id"
                    :enabled="item.status === 'confirmed'"
                    @refunded="getData"
                  />
                </td>
              </tr>
            </template>
          </template>

          <template #[`body.append`]>
            <template v-if="!loader">
              <tr class="table-summary">
                <td
                  colspan="7"
                  class="text-start font-weight-bold"
                >
                  {{ $t('common_totalOnPage') }}
                </td>
                <td class="text-end font-weight-bold border-right">
                  <template v-if="dataTable.items.length !== 0">
                    {{
                      dataTable.items|sumProperty('value')|currencySymbol(
                        dataTable.items[0]?.currency_symbol
                      )
                    }}
                  </template>
                  <template v-else>
                    -
                  </template>
                </td>
                <td />
              </tr>
              <tr>
                <td
                  colspan="7"
                  class="text-start font-weight-bold"
                >
                  {{ $t('turnover.table.total') }}
                </td>
                <td class="text-end font-weight-bold border-right">
                  <template v-if="dataTable.items.length !== 0">
                    {{
                      dataTable.sum|currencySymbol(dataTable.items[0]?.currency_symbol)
                    }}
                  </template>
                  <template v-else>
                    -
                  </template>
                </td>
                <td />
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <v-dialog
      v-model="dialogRefund"
      content-class="dialogWidth-2"
      style="z-index: 1300"
      width="50%"
    >
      <v-card>
        <v-card-title class="title">
          {{ $t('common_refund') }}
        </v-card-title>
        <v-card-text class="card-text-wrap">
          <div class="text-center">
            <v-icon
              large
              class="py-6 warning--text"
            >
              mdi-alert
            </v-icon>
            <h2>{{ $t('common_refund_question') }}</h2>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            text
            color="primary"
            :disabled="loaderRefund"
            @click.native="dialogRefund = false"
          >
            {{ $t('actions.cancel') }}
          </v-btn>
          <v-btn
            color="warning"
            class="white--text text-center"
            :loading="loaderRefund"
            @click.native="refund()"
          >
            {{ $t('commonAction_refund') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import DateRangePicker from '@components/common/DateRangePicker.vue';
import moment from 'moment';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import SalesDocumentMixin from '@components/common/mixins/SalesDocumentMixin.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import PaymentStatusMixin from '@components/common/badge/icon-text-mixin/PaymentStatusMixin.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import MultiselectValueMixin from '@components/mixins/MultiselectValueMixin.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import debounce from 'lodash/debounce';
import RefundDialog from './RefundDialog.vue';

export default {
  components: {
    ReportCreateModal,
    TextSearch,
    Multiselect,
    DateRangePicker,
    ActDownload,
    BtnRefresh,
    RefundDialog,
  },
  mixins: [
    ExportMixin,
    PaymentStatusMixin,
    SnackbarMixin,
    SalesDocumentMixin,
    MultiselectValueMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
    showFiltering: {
      type: Boolean,
      default: true,
    },
    userName: {
      type: String,
      default: null,
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    autoUpdateTransactions: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      exportItems: [
        {
          title: 'actions.export_csv',
          action: 'exportCsv',
          icon: 'get_app',
        },
        {
          title: 'actions.export_xlsx',
          action: 'exportXlsx',
          icon: 'description',
        },
      ],
      dialogRefund: false,
      autoLoadData: this.autoLoad,
      registerUserNameEvent: this.autoUpdateTransactions,
      loader: true,
      loaderRefund: false,
      refundPaymentId: null,
      advanceFiltering: this.showFiltering,
      advanced: false,
      pagination: {
        page: 1,
        itemsPerPage: 25,
        sortBy: ['createdAt'],
        sortDesc: [true],
      },
      possibleIssuers: [
      ],
      possibleStatuses: [
      ],
      filters: {
        statuses: {
          icons: {
            error: 'mdi-close-octagon-outline',
            confirmed: 'mdi-check-circle-outline',
            rejected: 'mdi-alert-outline',
            initiated: 'mdi-cached',
            timeout: 'mdi-clock-outline',
            waiting: 'mdi-cached',
            refunded: 'mdi-credit-card-refund-outline',
          },
          colors: {
            error: 'error',
            confirmed: 'green darken-2',
            rejected: 'error',
            initiated: 'progress',
            timeout: 'error',
            waiting: 'progress',
            refunded: 'warning',
          },
          texts: {
            error: this.$t('common_error'),
            confirmed: this.$t('common_paid'),
            rejected: this.$t('common_canceled'),
            initiated: this.$t('common_processing'),
            timeout: this.$t('common_timeout'),
            waiting: this.$t('common_processing'),
            refunded: this.$t('common_refund'),
          },
          text: this.$t('common_state'),
          icon: 'mdi-list-status',
        },
        issuers: {
          icons: {
            p24: 'mdi-bank',
            CREDIT_CARD: 'mdi-credit-card',
          },
          text: this.$t('loyalApp_externalType'),
          icon: 'mdi-cash-multiple',
        },
      },
      issuers: [
      ],
      status: [
      ],
      filtering: {
        footerProps: {
          'items-per-page-options': [25, 50, 100],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        search: {
          text: null,
          dateFrom: null,
          dateTo: null,
          hourFrom: null,
          hourTo: null,
          dateValue: null,
        },
        nameExists: null,
      },
      dataTable: {
        headers: [
          {
            text: this.$t('common_id'),
            value: 'id',
            sortable: false,
          },
          {
            text: this.$t('common_tableDate'),
            value: 'time',
            displayMethod: 'date',
            sortable: false,
          },
          {
            text: this.$t('common_user'),
            value: 'user',
            class: 'md-and-up',
            sortable: false,
          },
          {
            text: this.$t('common_fullCustomerName'),
            value: 'client_name',
            class: 'md-and-up text-start',
            sortable: false,
          },
          {
            text: this.$t('common_taxNumber'),
            value: 'client_nip',
            class: 'md-and-up text-end',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_accountType'),
            value: 'issuer',
            class: 'md-and-up',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_externalId'),
            value: 'external_id',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_transactionValue'),
            value: 'value',
            class: 'hidden-sm-and-down md-and-up text-end',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_salesDocument'),
            value: 'invoice',
            class: 'text-end',
            sortable: false,
          },
          {
            value: 'actions',
            text: this.$t('actions.actions'),
            align: 'end',
            sortable: false,
          },
        ],
        items: [],
        totalItems: 0,
        sum: 0,
        externalSum: 0,
      },
    };
  },
  computed: ({
    exportAsyncParams() {
      return {
        report: 'v2\\WlaExternalPaymentsReport',
        ...this.getParams(),
      };
    },
    statusesDisabled() {
      const statuses = this.possibleStatuses.map(
        (row) => ({
          text: this.filters.statuses.texts[row.name] ?? row.name,
          value: row.name,
          disabled: false,
          icon: this.filters.statuses.icons[row.name] ?? '',
        }),
      );

      return statuses;
    },
    issuerItems() {
      const issuers = this.possibleIssuers.map(
        (row) => ({
          text: row.name,
          value: row.name,
          disabled: false,
          icon: this.filters.issuers.icons[row.name] ?? '',
        }),
      );

      return issuers;
    },
  }),
  watch: {
    status() {
      this.pagination.page = 1;
      this.getDataDebounced();
    },
    issuers() {
      this.pagination.page = 1;
      this.getDataDebounced();
    },
    app() {
      this.pagination.page = 1;
      this.issuers = [];
    },
    filtering: {
      handler() {
        if (this.autoLoadData) {
          this.getDataDebounced();
        }
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if ((oldValue.sortDesc !== newValue.sortDesc
                || oldValue.page !== newValue.page
                || oldValue.itemsPerPage !== newValue.itemsPerPage
                || oldValue.sortBy !== newValue.sortBy)
            && this.autoLoadData) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
              || oldValue.sortBy !== newValue.sortBy
              || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          this.getData();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getData();
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.pagination.page = 1;
      this.getData();
    }, 1000);
  },
  methods: {
    getDateInUTC(date) {
      return moment(date).format('YYYY-MM-DD HH:mm:ss');
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getParams() {
      return {
        issuer: this.getMultiselectValue(this.possibleIssuers, this.issuers),
        app: this.app,
        status: this.getMultiselectValue(this.possibleStatuses, this.status),
        orderBy: this.pagination.sortBy[0],
        orderDescending: (this.pagination.sortDesc[0] === true)
          ? 1
          : 0,
        startDate: this.filtering.search.dateFrom
          ? `${this.filtering.search.dateFrom}`
          : null,
        endDate: this.filtering.search.dateTo
          ? `${this.filtering.search.dateTo}`
          : null,
        search: this.filtering.search.text,
      };
    },
    getData() {
      this.autoLoadData = true;
      this.loader = true;

      this.axios.get(
        '/api/loyalapp/externalpayments',
        {
          params: {
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
            ...this.getParams(),
          },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.dataTable.totalItems = Number(response.data.total);
            this.dataTable.items = response.data.data;
            this.possibleIssuers = response.data.filters.issuers;
            this.possibleStatuses = response.data.filters.statuses;
            this.loader = false;
            this.dataTable.sum = response.data.totalSum.value;
            this.dataTable.externalSum = response.data.paymentSum;
          }
        });
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    onDateRangeChange({
      from,
      to,
      value,
    }) {
      this.filtering.search.dateFrom = from;
      this.filtering.search.dateTo = to;
      this.filtering.search.dateValue = value;
    },
    openRefundDialog(exteranlPaymentID) {
      this.refundPaymentId = exteranlPaymentID;
      this.dialogRefund = true;
    },
    refund() {
      this.loaderRefund = true;
      this.axios.post(
        `/api/gateway/wla-admin/external-payment/${this.refundPaymentId}/refund`,
        {
          app: this.app,
        },
      )
        .then(
          () => {
            this.loaderRefund = false;
            this.dialogRefund = false;
            this.showSnackbar('success', this.$t('common_actionSucced'));
            this.getData();
          },
          () => {
            this.loaderRefund = false;
            this.showSnackbar('error', this.$t('common_errorHeader'));
          },
        );
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

.display-flex {
  display: flex;
}

.flex-inline-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-inline-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

</style>
