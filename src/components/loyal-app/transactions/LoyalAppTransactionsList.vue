<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        lg="12"
        xl="12"
        class="px-0"
      >
        <v-layout
          row
          wrap
        >
          <v-col
            md="8"
            sm="12"
            cols="12"
          >
            <v-layout
              row
              justify-start
              wrap
            >
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <text-search
                  v-model="text"
                  :disabled="loader"
                  @input="getDataDebounced"
                />
              </v-col>
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <multiselect
                  v-model="status"
                  :items="statusesDisabled"
                  :label="filters.statuses.text"
                  :prepend-icon="filters.statuses.icon"
                  :return-array="true"
                  :disabled="loader"
                  unified
                  dense
                  allow-null
                />
              </v-col>
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <multiselect
                  v-model="type"
                  :items="typesDisabled"
                  :label="filters.types.text"
                  :prepend-icon="filters.types.icon"
                  :return-array="true"
                  :disabled="loader"
                  unified
                  allow-null
                />
              </v-col>
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <multiselect
                  v-model="topUpIssuer"
                  :items="issuersDisabled"
                  :label="filters.issuers.text"
                  :prepend-icon="filters.issuers.icon"
                  :return-array="true"
                  :disabled="loader"
                  unified
                  allow-null
                />
              </v-col>
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <multiselect
                  v-model="extPay"
                  :items="extPayDisabled"
                  :label="filters.extPay.text"
                  :prepend-icon="filters.extPay.icon"
                  :return-array="true"
                  :disabled="loader"
                  unified
                  allow-null
                />
              </v-col>
            </v-layout>
          </v-col>
          <v-col
            md="4"
            sm="12"
            cols="12"
          >
            <date-range-picker
              key="dateRangeLoyalAppUsage"
              ref="dateRangeLoyalAppUsage"
              :show-presets="true"
              start-preset="currentMonth"
              @reload-transaction-list="onDateRangeChange"
            />
          </v-col>
        </v-layout>
        <v-layout
          row
          wrap
        >
          <v-col
            sm="12"
            cols="12"
            class="pt-1 pb-1 pr-1 pl-1"
          >
            <v-layout
              row
              wrap
            >
              <v-col
                cols="12"
                sm="12"
                class="display-flex align-center pt-1 pb-1 px-3"
              >
                <v-layout
                  justify-end
                  align-center
                >
                  <btn-refresh
                    class="mt-0"
                    size="small"
                    :disabled="loader"
                    @click="getData"
                  />
                  <report-create-modal
                    btn-class="ml-2"
                    :params="exportAsyncParams"
                    :disabled="loader"
                    :preset="filtering.search.dateValue"
                  />
                </v-layout>
              </v-col>
            </v-layout>
          </v-col>
        </v-layout>
        <v-layout
          row
          wrap
        />
      </v-col>
      <v-col cols="12">
        <div
          v-if="loader"
          class="loader-background"
        />
        <v-data-table
          v-resize="onResize"
          mobile-breakpoint="0"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :loading="loader"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
          :footer-props="filtering.footerProps"
          :single-expand="true"
        >
          <template #progress>
            <div class="text-center">
              <v-progress-circular
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <template #item="{ item, expand, isExpanded }">
            <template v-if="!loader">
              <tr @click="expand(!isExpanded)">
                <td class="text-sm-start">
                  <div class="flex-inline-start">
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-icon
                          :color="getStatusDetails(item.status).color"
                          v-bind="attrs"
                          v-on="on"
                        >
                          {{ getStatusDetails(item.status).icon }}
                        </v-icon>
                      </template>
                      <span> {{ getStatusDetails(item.status).text }}</span>
                    </v-tooltip>
                    {{ item.id }}
                  </div>
                </td>
                <td class="text-sm-start border-right text-no-wrap">
                  <template v-if="null !== item.time">
                    {{ getDateInUTC(item.time) }}
                  </template>
                </td>
                <td class="text-sm-start">
                  {{ item.bkfpay_user_email ?? '-' }}
                </td>
                <td class="text-sm-start border-right">
                  {{ item.bkfpay_company_email ?? '-' }}
                </td>
                <td class="text-sm-start">
                  {{ item.issuer }}
                </td>
                <td class="text-sm-start">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <v-icon
                        v-bind="attrs"
                        v-on="on"
                      >
                        {{ getTopUpProgressIcon(item.transaction_type).icon }}
                      </v-icon>
                    </template>
                    <span>{{ getTopUpProgressIcon(item.transaction_type).toltipText }}</span>
                  </v-tooltip>
                </td>
                <td class="text-end hidden-sm-and-down md-and-up border-right">
                  {{
                    item.value|currencySymbol(item.currency_symbol)
                  }}
                </td>
                <td class="text-end text-no-wrap">
                  <template v-if="item.document_id !== null">
                    <div class="flex-inline-end">
                      <div>
                        {{ item.document_number }}
                      </div>
                      <v-icon>
                        {{ getDocumentTypeIcon(item.document_type) }}
                      </v-icon>
                      <act-download
                        v-if="item.document_uri"
                        :url="`/api/gateway/wla-admin${item.document_uri}?app=${app}`"
                      />
                    </div>
                  </template>
                  <template
                    v-if="item.document_id == null"
                  >
                    -
                  </template>
                </td>
                <td
                  class="text-end"
                >
                  <v-icon v-if="isExpanded">
                    mdi-chevron-up
                  </v-icon>
                  <v-icon v-else>
                    mdi-chevron-down
                  </v-icon>
                </td>
              </tr>
            </template>
          </template>

          <template #expanded-item="{ headers, item }">
            <td
              v-if="!loader"
              :colspan="headers.length"
            >
              <details-table :data="item.info ?? []" />
            </td>
          </template>

          <template #[`body.append`]>
            <template v-if="!loader">
              <tr class="table-summary">
                <td
                  colspan="6"
                  class="text-start font-weight-bold"
                >
                  {{ $t('common_totalOnPage') }}
                </td>
                <td class="text-end font-weight-bold border-right">
                  <template v-if="dataTable.items.length !== 0">
                    {{
                      dataTable.items|sumProperty('value')|currencySymbol(
                        dataTable.items[0]?.currency_symbol
                      )
                    }}
                  </template>
                  <template v-else>
                    -
                  </template>
                </td>
                <td class="text-end font-weight-bold" />
                <td colspan="2" />
              </tr>
              <tr>
                <td
                  colspan="6"
                  class="text-start font-weight-bold"
                >
                  {{ $t('turnover.table.total') }}
                </td>
                <td class="text-end font-weight-bold border-right">
                  <template v-if="dataTable.items.length !== 0">
                    {{
                      dataTable.sum|currencySymbol(dataTable.items[0]?.currency_symbol)
                    }}
                  </template>
                  <template v-else>
                    -
                  </template>
                </td>
                <td class="text-end font-weight-bold" />
                <td colspan="2" />
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import DateRangePicker from '@components/common/DateRangePicker.vue';
import moment from 'moment';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import SalesDocumentMixin from '@components/common/mixins/SalesDocumentMixin.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import DetailsTable from '@components/loyal-app/transactions/DetailsTable.vue';
import PaymentStatusMixin from '@components/common/badge/icon-text-mixin/PaymentStatusMixin.vue';
import debounce from 'lodash/debounce';
import TextSearch from '@components/common/filters/TextSearch.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';

export default {
  components: {
    ActDownload,
    ReportCreateModal,
    TextSearch,
    Multiselect,
    DateRangePicker,
    DetailsTable,
    BtnRefresh,
  },
  mixins: [
    ExportMixin,
    PaymentStatusMixin,
    SnackbarMixin,
    SalesDocumentMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
    showFiltering: {
      type: Boolean,
      default: true,
    },
    userNumber: {
      type: Number,
      default: null,
    },
    userName: {
      type: String,
      default: null,
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    autoUpdateTransactions: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      text: null,
      exportItems: [
        {
          title: 'actions.export_csv',
          action: 'exportCsv',
          icon: 'get_app',
        },
        {
          title: 'actions.export_xlsx',
          action: 'exportXlsx',
          icon: 'description',
        },
      ],
      autoLoadData: this.autoLoad,
      registerUserNameEvent: this.autoUpdateTransactions,
      loader: true,
      advanceFiltering: this.showFiltering,
      advanced: false,
      pagination: {
        page: 1,
        itemsPerPage: 25,
        sortBy: ['createdAt'],
        sortDesc: [true],
      },
      possibleExtPay: [
      ],
      possibleIssuers: [
      ],
      possibleTypes: [
      ],
      possibleStatuses: [
      ],
      filters: {
        extPay: {
          icons: {
            P24: 'mdi-bank',
            TRANSFER: 'mdi-bank-transfer',
            CREDIT_CARD: 'mdi-credit-card',
            POST_PAID_TPBP: 'mdi-credit-card-clock-outline',
            ESPAGO: 'mdi-credit-card',
          },
          text: this.$t('loyalApp_externalType'),
          icon: 'mdi-cash-multiple',
        },
        issuers: {
          icons: {
            CREDIT_CARD_I2M: 'mdi-credit-card',
            CREDIT_CARD: 'mdi-credit-card',
            BKFPAY: 'mdi-cellphone-wireless',
            EXTERNAL_PAYMENT: 'mdi-bank-transfer-in',
            VIRTUAL_CARD: 'mdi-wallet-membership',
            P24: 'mdi-bank',
            PAY_EX: 'mdi-credit-card',
            BE_LOYAL: 'mdi-card_membership',
            POST_PAID_TPBP: 'mdi-trending-up',
          },
          text: this.$t('loyalApp_accountType'),
          icon: 'mdi-wallet',
        },
        statuses: {
          icons: {
            error: 'mdi-close-octagon-outline',
            confirmed: 'mdi-check-circle-outline',
            rejected: 'mdi-alert-outline',
            initiated: 'mdi-cached',
            timeout: 'mdi-clock-outline',
            waiting: 'mdi-cached',
            refunded: 'mdi-credit-card-refund-outline',
          },
          colors: {
            error: 'error',
            confirmed: 'green darken-2',
            rejected: 'error',
            initiated: 'progress',
            timeout: 'error',
            waiting: 'progress',
            refunded: 'warning',
          },
          texts: {
            error: this.$t('common_error'),
            confirmed: this.$t('common_paid'),
            rejected: this.$t('common_canceled'),
            initiated: this.$t('common_processing'),
            timeout: this.$t('common_timeout'),
            waiting: this.$t('common_processing'),
            refunded: this.$t('common_refund'),
          },
          text: this.$t('common_state'),
          icon: 'mdi-list-status',
        },
        types: {
          icons: {
            TOP_UP: 'mdi-trending-up',
            PAYMENT: 'mdi-trending-down',
            TOP_UP_BONUS: 'mdi-sale',
            TOP_UP_CODE: 'mdi-card-plus',
            ACCOUNT_DELETE: 'mdi-delete',
          },
          text: this.$t('common_transactionType'),
          icon: 'mdi-trending-up',
        },
      },
      extPay: [
      ],
      topUpIssuer: [
      ],
      type: [
      ],
      status: [
      ],
      filtering: {
        footerProps: {
          'items-per-page-options': [25, 50, 100],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        search: {
          dateFrom: null,
          dateTo: null,
          hourFrom: null,
          hourTo: null,
          dateValue: null,
        },
        nameExists: null,
      },
      dataTable: {
        headers: [
          {
            text: this.$t('common_id'),
            value: 'id',
            sortable: false,
          },
          {
            text: this.$t('common_tableDate'),
            value: 'time',
            displayMethod: 'date',
            sortable: false,
          },
          {
            text: this.$t('common_user'),
            value: 'bkfpay_user',
            class: 'md-and-up',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_account'),
            value: 'bkfpay_company',
            class: 'md-and-up',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_accountType'),
            value: 'issuer',
            class: 'md-and-up',
            sortable: false,
          },
          {
            text: this.$t('common_transactionType'),
            value: 'transaction_type',
            class: 'md-and-up',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_transactionValue'),
            value: 'payment_value',
            class: 'hidden-sm-and-down md-and-up text-end',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_salesDocument'),
            value: 'invoice',
            class: 'text-end',
            sortable: false,
          },
          {
            text: '',
            value: '',
            class: 'text-end',
            sortable: false,
          },
        ],
        items: [],
        totalItems: 0,
        sum: 0,
        externalSum: 0,
      },
    };
  },
  computed: ({
    exportAsyncParams() {
      return {
        report: 'v2\\WlaTransactionsReport',
        ...this.getParams(),
      };
    },
    statusesDisabled() {
      const statuses = this.possibleStatuses.map(
        (row) => ({
          text: this.filters.statuses.texts[row.name] ?? '',
          value: row.name,
          disabled: false,
          icon: this.filters.statuses.icons[row.name] ?? '',
        }),
      );

      return statuses;
    },
    extPayDisabled() {
      const issuers = this.possibleExtPay.map(
        (row) => ({
          text: row.name,
          value: row.name,
          disabled: false,
          icon: this.filters.extPay.icons[row.name] ?? '',
        }),
      );

      return issuers;
    },
    issuersDisabled() {
      const issuers = this.possibleIssuers.map(
        (row) => ({
          text: row.name,
          value: row.name,
          disabled: false,
          icon: this.filters.issuers.icons[row.name] ?? '',
        }),
      );

      return issuers;
    },
    typesDisabled() {
      const types = this.possibleTypes.map(
        (row) => ({
          text: row.name,
          value: row.name,
          disabled: false,
          icon: this.filters.types.icons[row.name] ?? '',
        }),
      );

      return types;
    },
  }),
  watch: {
    status() {
      this.pagination.page = 1;
      this.getDataDebounced();
    },
    type() {
      this.pagination.page = 1;
      this.getDataDebounced();
    },
    topUpIssuer() {
      this.pagination.page = 1;
      this.getDataDebounced();
    },
    extPay() {
      this.pagination.page = 1;
      this.getDataDebounced();
    },
    app() {
      this.issuers = [];
      this.topUpIssuer = [];
      this.pagination.page = 1;
    },
    filtering: {
      handler() {
        if (this.autoLoadData) {
          this.getDataDebounced();
        }
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if ((oldValue.sortDesc !== newValue.sortDesc
                || oldValue.page !== newValue.page
                || oldValue.itemsPerPage !== newValue.itemsPerPage
                || oldValue.sortBy !== newValue.sortBy)
            && this.autoLoadData) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
              || oldValue.sortBy !== newValue.sortBy
              || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          this.getData();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getData();
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.pagination.page = 1;
      this.getData();
    }, 1000);
  },
  methods: {
    getParams() {
      return {
        issuer: this.getToggleIssuerFiltering(),
        app: this.app,
        orderBy: this.pagination.sortBy[0],
        orderDescending: (this.pagination.sortDesc[0] === true)
          ? 1
          : 0,
        user: this.userNumber,
        type: this.getToggleTypeFiltering(),
        status: this.getToggleStatusFiltering(),
        extPay: this.getToggleExternalPayFiltering(),
        startDate: this.filtering.search.dateFrom
          ? `${this.filtering.search.dateFrom}`
          : null,
        endDate: this.filtering.search.dateTo
          ? `${this.filtering.search.dateTo}`
          : null,
        search: this.text,
      };
    },
    getDateInUTC(date) {
      return moment(date).format('YYYY-MM-DD HH:mm:ss');
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getToggleIssuerFiltering() {
      return this.topUpIssuer.length ? this.topUpIssuer.join(',') : '';
    },
    getToggleTypeFiltering() {
      return this.type.length ? this.type.join(',') : '';
    },
    getToggleStatusFiltering() {
      return this.status.length ? this.status.join(',') : '';
    },
    getToggleExternalPayFiltering() {
      return this.extPay.length ? this.extPay.join(',') : '';
    },
    getData() {
      this.autoLoadData = true;
      this.loader = true;

      this.axios.get(
        '/api/loyalapp/transactions',
        {
          params: {
            pageNumber: this.pagination.page,
            itemsPerPage: this.pagination.itemsPerPage,
            ...this.getParams(),
          },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.dataTable.totalItems = Number(response.data.total);
            this.dataTable.items = response.data.data;
            this.possibleTypes = response.data.filters.type;
            this.possibleStatuses = response.data.filters.status;
            this.possibleIssuers = response.data.filters.issuers;
            this.possibleExtPay = response.data.filters.extPay;
            this.loader = false;
            this.dataTable.sum = response.data.totalSum.value;
            this.dataTable.externalSum = response.data.paymentSum;
          }
        });
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    getTopUpProgressIcon(type) {
      if (type === 'TOP_UP') {
        return {
          icon: 'mdi-trending-up',
          toltipText: this.$t('loyalApp_topup'),
        };
      }
      if (type === 'PAYMENT') {
        return {
          icon: 'mdi-trending-down',
          toltipText: this.$t('loyalApp_payment'),
        };
      }

      if (type === 'TOP_UP_BONUS') {
        return {
          icon: 'mdi-sale',
          toltipText: this.$t('loyalApp_topupBonus'),
        };
      }
      if (type === 'TOP_UP_CODE') {
        return {
          icon: 'mdi-card-plus',
          toltipText: this.$t('common_topupCode'),
        };
      }
      if (type === 'ACCOUNT_DELETE') {
        return {
          icon: 'mdi-delete',
          toltipText: this.$t('common_accountDelete'),
        };
      }

      return {
        icon: 'question',
        toltipText: this.$t('unknown'),
      };
    },
    onDateRangeChange({
      from,
      to,
      value,
    }) {
      this.filtering.search.dateFrom = from;
      this.filtering.search.dateTo = to;
      this.filtering.search.dateValue = value;
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

.display-flex {
  display: flex;
}

.flex-inline-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-inline-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

</style>
