<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        sm="12"
        md="12"
        class="px-0"
      >
        <v-row>
          <v-col
            cols="12"
            sm="4"
          >
            <v-text-field
              v-model="filtering.search.text"
              :label="$t('common_search')"
              hide-details
              prepend-icon="mdi-magnify"
            />
          </v-col>
          <v-col
            cols="12"
            sm="4"
          >
            <multiselect
              v-model="codeUsed"
              :items="code.options"
              :label="code.text"
              :prepend-icon="code.icon"
              :disabled="loader"
              unified
              allow-null
            />
          </v-col>
          <v-col
            cols="12"
            sm="4"
          >
            <date-range-picker
              key="dateRange"
              ref="dateRange"
              prepend-icon="mdi-calendar-range"
              :show-presets="true"
              start-preset="currentMonth"
              @reload-transaction-list="onDateRangeChange"
            />
          </v-col>
        </v-row>
      </v-col>
      <v-col
        cols="12"
        sm="12"
        class="px-3"
      >
        <v-layout
          row
          wrap
          justify-end
          class="action-btn"
        >
          <btn-refresh
            class="mt-0"
            size="small"
            :disabled="loader"
            @click="getData"
          />
          <v-btn
            small
            color="primary"
            class="mt-1"
            @click.native="openModal('addCodeDialog')"
          >
            <v-icon left>
              mdi-plus
            </v-icon>
            {{ $t('actions.add_code') }}
          </v-btn>
          <div>
            <v-menu offset-y>
              <template #activator="{ on }">
                <v-btn
                  small
                  transition="slide-y-transition"
                  class="expand-menu ml-1 mt-1"
                  color="secondary"
                  dark
                  v-on="on"
                >
                  {{ $t('actions.export') }}
                  <v-icon right>
                    mdi-menu-down
                  </v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="(item, index) in exportItems"
                  :key="index"
                  @click="exportMenuAction(item.action)"
                >
                  <v-list-item-content>
                    <v-list-item-title>
                      {{ $t(item.title) }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </v-layout>
      </v-col>
      <v-col
        cols="12"
        class="px-0"
      >
        <v-data-table
          v-resize="onResize"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :loading="loader"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
          :footer-props="dataTable.footerProps"
        >
          <template #progress>
            <div class="text-center">
              <v-progress-circular
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <template #item="{ item }">
            <template v-if="!loader">
              <tr>
                <td class="text-sm-start">
                  {{ item.group_name }}
                </td>
                <td class="text-sm-center">
                  {{ item.promotional_code }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up ">
                  {{ item.value }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up ">
                  {{ item.ctime }}
                </td>
                <td class="text-sm-center hidden-xs-only">
                  <v-icon v-if="item.code_used">
                    mdi-check-circle-outline
                  </v-icon>
                  <v-icon v-else>
                    mdi-close-circle-outline
                  </v-icon>
                </td>
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <code-add-modal
      ref="addCodeDialog"
      :app="app"
    />
  </v-container>
</template>

<script>
import formatDate from 'date-fns/format';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import Multiselect from '@/components/common/filters/Multiselect.vue';
import CodeAddModal from './CodeAddModal.vue';

export default {
  components: {
    DateRangePicker,
    CodeAddModal,
    BtnRefresh,
    Multiselect,
  },
  mixins: [
    SnackbarMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      advanced: false,
      searchText: '',
      filter: '',
      codeUsed: null,
      code: {
        text: this.$t('common_state'),
        icon: 'mdi-list-status',
        options: [
          {
            text: this.$t('loyalApp_codeUsed'),
            value: 1,
            icon: 'mdi-check-circle-outline',
          },
          {
            text: this.$t('loyalApp_unusedCode'),
            value: 0,
            icon: 'mdi-close-circle-outline',
          },
        ],
      },
      filtering: {
        search: {
          text: '',
          dateFrom: '',
          dateTo: '',
        },
      },
      pagination: {
        page: 1,
        itemsPerPage: 25,
        sortBy: ['ctime'],
        sortDesc: [true],
      },
      exportItems: [
        {
          title: 'actions.export_csv',
          action: 'exportCsv',
          icon: 'get_app',
        },
        {
          title: 'actions.export_xlsx',
          action: 'exportXlsx',
          icon: 'description',
        },
        {
          title: 'actions.export_pdf',
          action: 'exportPdf',
          icon: 'insert_drive_file',
        },
      ],
      loader: true,
      dataTable: {
        footerProps: {
          'items-per-page-options': [10, 25, 50],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('loyalApp_groupName'),
            value: 'group_name',
            class: 'text-sm-start',
            showInRowExpand: false,
          },
          {
            text: this.$t('loyalApp_promotionalCode'),
            value: 'promotional_code',
            class: 'text-sm-center',
            showInRowExpand: false,
          },
          {
            text: this.$t('common_value'),
            value: 'value',
            class: 'hidden-md-and-down md-and-up text-sm-center',
            showInRowExpand: true,
          },
          {
            text: this.$t('loyalApp_ctime'),
            value: 'ctime',
            class: 'hidden-md-and-down md-and-up text-sm-center',
            showInRowExpand: true,
          },
          {
            text: this.$t('loyalApp_codeUsed'),
            value: 'code_used',
            class: 'text-sm-center hidden-xs-only',
            showInRowExpand: true,
            sortable: false,
          },
        ],
        items: [],
        totalItems: 0,
      },
    };
  },
  watch: {
    app() {
      this.getData();
    },
    filtering: {
      handler(searchString) {
        if (searchString.search.text !== null) {
          const result = searchString.search.text.match(/^((?!%|\?|!|\)|\(|'|\\|\/|&).)*$/g);
          if (result !== null) {
            this.getData();
          }
        } else {
          this.getData();
        }
      },
      deep: true,
    },
    codeUsed: {
      handler() {
        this.getData();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.sortDesc !== newValue.sortDesc
          || oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage
          || oldValue.sortBy !== newValue.sortBy) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          this.getData();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    toggleFilteringOption(groupIndex, optionIndex) {
      const inverted = !this.filters[groupIndex].options[optionIndex].active;
      this.filters[groupIndex].options[optionIndex].active = inverted;
    },
    onDateRangeChange({
      from,
      to,
    }) {
      this.filtering.search.dateFrom = from;
      this.filtering.search.dateTo = to;
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    exportMenuAction(actionName) {
      if (actionName === 'exportCsv') {
        this.getDataForExport('csv');
      } else if (actionName === 'exportXlsx') {
        this.getDataForExport('xlsx');
      } else if (actionName === 'exportPdf') {
        this.getDataForExport('pdf');
      }
    },
    getDataForExport(fileType = 'csv') {
      this.axios.get(
        '/api/loyalapp/promotionalcodes',
        {
          params: {
            page: this.pagination.page,
            orderBy: this.pagination.sortBy[0],
            orderDescending: (this.pagination.sortDesc[0] === true) ? 1 : 0,
            itemsPerPage: this.dataTable.totalItems,
            fullTextSearch: this.filtering.search.text,
            dateFrom: this.filtering.search.dateFrom,
            dateTo: this.filtering.search.dateTo,
            codeUsed: this.codeUsed,
            app: this.app,
          },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            const fileName = this.getExportFileName(
              this.$t('loyalApp_promotionalCodes'),
              fileType,
            );

            const dataToExport = this.mapForExport(response.data.items);
            if (!this.exporter.export(dataToExport, fileType, fileName)) {
              this.showSnackbar(
                'error',
                this.$t('transactions.export_error'),
              );
            }
          }
        });
    },
    getExportFileName(name, fileType) {
      const today = formatDate(new Date(), 'YYYY-MM-DD');
      const nameArray = [[name, today].join('_'), fileType];

      return nameArray.join('.');
    },
    mapForExport(data) {
      const headers = [
        this.$t('number'),
        this.$t('loyalApp_promotionalCode'),
        this.$t('common_value'),
        this.$t('loyalApp_codeUsed'),
      ];

      const filtered = data.map(
        (row, index) => [
          index + 1,
          row.promotional_code,
          row.value,
          row.code_used ? this.$t('loyalApp_yes') : this.$t('loyalApp_no'),
        ],
      );
      filtered.unshift(headers);

      return filtered;
    },
    getData() {
      this.loader = true;
      this.axios.get(
        '/api/loyalapp/promotionalcodes',
        {
          params: {
            page: this.pagination.page,
            orderBy: this.pagination.sortBy[0],
            orderDescending: (this.pagination.sortDesc[0] === true) ? 1 : 0,
            itemsPerPage: this.pagination.itemsPerPage,
            fullTextSearch: this.filtering.search.text,
            dateFrom: this.filtering.search.dateFrom,
            dateTo: this.filtering.search.dateTo,
            codeUsed: this.codeUsed,
            app: this.app,
          },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.dataTable.totalItems = response.data.count;
            this.dataTable.items = response.data.items;
            this.loader = false;
          }
        })
        .catch(() => {
          this.showSnackbar(
            'error',
            this.$t('common_errorHeader'),
          );
        });
    },
  },
};
</script>

<style scoped>
.filters {
  width: 70%;
}

.action-btn {
  justify-content: flex-end;
}

@media only screen and (max-width: 767px) {
  .action-btn {
    justify-content: center;
  }
}

.expand-table {
  width: 100%
}

.filter-btn {
  width: 72%;
}
</style>
