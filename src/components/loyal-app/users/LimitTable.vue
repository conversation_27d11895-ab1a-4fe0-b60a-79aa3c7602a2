<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <h3 class="mb-3">
      {{ headerText }}
    </h3>
    <v-simple-table
      v-if="items.length"
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td class="border-right" />
            <td
              v-for="[key, header] in headers"
              :key="`headers-${key}`"
              class="border-right font-weight-bold"
            >
              {{ header }}
            </td>
          </tr>
          <tr
            v-for="[key, value] in items"
            :key="`item-${key}`"
          >
            <td
              :key="`itemName-${key}`"
              class="border-right text-left font-weight-bold"
            >
              {{ key }}
            </td>
            <template
              v-for="[key2, arrayItemValue] in Object.entries(value)"
            >
              <td
                :key="`item-${key}-item-array-value-${key2}`"
                class="border-right"
              >
                <template v-if="isNumberParsedAsString(arrayItemValue)">
                  {{ arrayItemValue|currencySymbol(currencySymbol) }}
                </template>
                <template v-else>
                  {{ arrayItemValue ?? '-' }}
                </template>
              </td>
            </template>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-card-text v-else>
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>
import i18n from '@/i18n';
import NumericValueMixin from '@components/mixins/NumericValueMixin.vue';

export default {
  name: 'LimitTable',
  mixins: [
    NumericValueMixin,
  ],
  props: {
    data: {
      type: [Object, Array],
      required: true,
    },
    currencySymbol: {
      type: String,
      default: undefined,
      required: false,
    },
    headerText: {
      type: String,
      default: () => i18n.t('fiscal_transactions.details.heading'),
      required: false,
    },
  },
  computed: {
    headers() {
      if (typeof this.data === 'undefined') {
        return [];
      }

      const firstKey = Object.keys(this.data)[0];

      if (typeof firstKey === 'undefined') {
        return [];
      }

      return Object.entries(Object.keys(this.data[firstKey]));
    },
    items() {
      if (typeof this.data === 'undefined') {
        return [];
      }

      return Object.entries(this.data);
    },
  },
};
</script>
