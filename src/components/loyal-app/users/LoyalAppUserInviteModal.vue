<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on, attrs }">
        <v-btn
          tile
          rounded
          class="mr-2 my-1"
          elevation="1"
          v-bind="attrs"
          x-small
          fab
          color="secondary"
          v-on="on"
          @click="open"
        >
          <v-icon>
            mdi-account-plus
          </v-icon>
        </v-btn>
      </template>
      <span>{{ $t('actions.invite_user') }}</span>
    </v-tooltip>

    <GenericModal
      v-model="dialog"
      :title="$t('actions.invite_user')"
      :confirm-button-text="$t('actions.send_invitation')"
      :cancel-button-text="$t('actions.return_to_list')"
      max-width="600"
      :loading="loaders.submit"
      :disabled="!isFormValid"
      @close="clearFormData"
      @submit="submit"
    >
      <v-layout wrap>
        <v-col
          cols="12"
          sm="12"
          md="12"
        >
          <v-alert
            :value="true"
            border="left"
            type="info"
            color="blue lighten-4"
            class="blue--text text--darken-2"
          >
            {{ $t('loyalApp_invitationInfo') }}
          </v-alert>
        </v-col>
      </v-layout>
      <v-container grid-list-md>
        <v-layout wrap>
          <v-col
            cols="12"
            sm="12"
            md="12"
          >
            <v-text-field
              v-model="email"
              prepend-icon="mdi-account"
              :label="$t('common_email') + ' *'"
              :rules="form.validationRules.email"
              required
              :validate-on-blur="form.validateOnBlur"
            />
            <v-autocomplete
              v-model="selectedPackage"
              :items="packagesOptions"
              item-value="id"
              item-text="displayText"
              prepend-icon="mdi-cash"
              :label="$t('loyalApp_promotional_package')"
              :loading="packagesLoader"
              :disabled="packagesLoader"
              clearable
            />

            <invoice-data-form
              ref="invoiceDataForm"
              v-model="invoiceData"
              :expanded.sync="invoiceDataExpanded"
              :validate-on-blur="form.validateOnBlur"
              @validation-change="onInvoiceDataValidationChange"
            />

            <small>*{{ $t('common_fieldRequired') }}</small>
          </v-col>
        </v-layout>
      </v-container>
    </GenericModal>
  </div>
</template>

<script>
import SnackbarMixin from '@/components/mixins/SnackbarMixin.vue';
import GenericModal from '@/components/common/GenericModal.vue';
import InvoiceDataForm from './InvoiceDataForm.vue';

export default {
  name: 'LoyalAppUserInviteModal',
  components: {
    GenericModal,
    InvoiceDataForm,
  },
  mixins: [
    SnackbarMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      dialog: false,
      loaders: {
        submit: false,
      },
      email: '',
      selectedPackage: null,
      packages: [],
      packagesLoader: false,
      invoiceDataExpanded: null,
      invoiceData: {
        name: '',
        taxNumber: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      },
      form: {
        validateOnBlur: true,
        valid: true,
        invoiceDataValid: true,
        validationRules: {
          email: [
            (v) => /^([a-zA-Z0-9_\-.+]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/.test(
              v,
            )
              || this.$t('loyalApp_invalidValue'),
          ],
        },
      },
    };
  },
  computed: {
    packagesOptions() {
      return this.packages.map((pkg) => ({
        ...pkg,
        displayText: `${pkg.title} (${this.$t('loyaltyCards_value')}: ${pkg.package_value})`,
      }));
    },
    companyData() {
      return {
        name: this.invoiceData.name === '' ? null : this.invoiceData.name || null,
        taxNumber: this.invoiceData.taxNumber === '' ? null : this.invoiceData.taxNumber || null,
        address: this.invoiceData.address === '' ? null : this.invoiceData.address || null,
        postCode: this.invoiceData.postCode === '' ? null : this.invoiceData.postCode || null,
        city: this.invoiceData.city === '' ? null : this.invoiceData.city || null,
        country: this.invoiceData.country === '' ? null : this.invoiceData.country || null,
      };
    },
    isFormValid() {
      return this.form.valid && this.form.invoiceDataValid;
    },
  },
  created() {
    this.getPackages();
  },
  methods: {
    open() {
      this.dialog = true;
    },
    close() {
      this.dialog = false;
    },
    async submit() {
      // Sprawdź walidację przed wysłaniem
      if (!this.isFormValid) {
        this.showSnackbar('error', this.$t('common_error_occurred'));
        return;
      }

      this.loaders.submit = true;
      try {
        const response = await this.axios.post('/api/gateway/wla-admin/users', {
          email: this.email,
          app: this.app,
          packageId: this.selectedPackage,
          companyData: this.companyData,
        });

        if (response.status === 200) {
          this.showSnackbar('success', this.$t('common_actionSucced'));
          this.close();
          this.$emit('reload');
        }
      } catch (error) {
        if (error.request && error.request.status === 409) {
          this.showSnackbar('warning', this.$t('loyalApp_userAlreadyExists'));
        } else {
          this.showSnackbar('error', this.$t('common_error_occurred'));
        }
      } finally {
        this.loaders.submit = false;
      }
    },
    onInvoiceDataValidationChange(isValid) {
      this.form.invoiceDataValid = isValid;
    },
    clearFormData() {
      this.email = '';
      this.selectedPackage = null;
      this.invoiceDataExpanded = null;
      if (this.$refs.invoiceDataForm) {
        this.$refs.invoiceDataForm.clearData();
      }
    },
    async getPackages() {
      this.packagesLoader = true;
      try {
        const response = await this.axios.get('/api/loyalapp/packages', {
          params: {
            app: this.app,
          },
        });
        if (response.status === 200 && response.data.items) {
          this.packages = response.data.items.filter((pkg) => pkg.active);
        }
      } catch (error) {
        this.showSnackbar('error', this.$t('common_error_occurred'));
      } finally {
        this.packagesLoader = false;
      }
    },
  },
};
</script>
