<template>
  <v-expansion-panels
    v-model="expandedInternal"
    class="mb-3"
  >
    <v-expansion-panel>
      <v-expansion-panel-header>
        <v-row>
          <v-icon>mdi-file-document-edit-outline</v-icon>
          <h6 class="text-h6 ml-2">
            {{ $t('common_dataToInvoice') }}
          </h6>
        </v-row>
      </v-expansion-panel-header>
      <v-expansion-panel-content>
        <v-text-field
          v-model="invoiceDataInternal.taxNumber"
          prepend-icon="mdi-card-account-details"
          :label="$t('common_taxNumber')"
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
        <v-text-field
          v-model="invoiceDataInternal.name"
          prepend-icon="mdi-domain"
          :label="$t('table.company_name')"
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
        <v-text-field
          v-model="invoiceDataInternal.address"
          prepend-icon="mdi-map-marker"
          :label="$t('common_address')"
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
        <v-text-field
          v-model="invoiceDataInternal.postCode"
          prepend-icon="mdi-mailbox"
          :label="$t('common_postCode')"
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
        <v-text-field
          v-model="invoiceDataInternal.city"
          prepend-icon="mdi-city"
          :label="$t('common_city')"
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
        <v-select
          v-model="invoiceDataInternal.country"
          :items="countries"
          item-text="name"
          item-value="shortName"
          prepend-icon="mdi-earth"
          :label="$t('common_country')"
          :loading="countriesLoader"
          :disabled="countriesLoader"
          clearable
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
      </v-expansion-panel-content>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script>
import SnackbarMixin from '@/components/mixins/SnackbarMixin.vue';

export default {
  name: 'InvoiceDataForm',
  mixins: [
    SnackbarMixin,
  ],
  props: {
    value: {
      type: Object,
      default: () => ({
        name: '',
        taxNumber: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      }),
    },
    expanded: {
      type: [Number, Array],
      default: null,
    },
    validateOnBlur: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      countries: [],
      countriesLoader: false,
      invoiceDataInternal: {
        name: '',
        taxNumber: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      },
    };
  },
  computed: {
    expandedInternal: {
      get() {
        return this.expanded;
      },
      set(value) {
        this.$emit('update:expanded', value);
      },
    },
  },
  watch: {
    value: {
      handler(newValue) {
        this.invoiceDataInternal = { ...newValue };
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.getCountries();
  },
  methods: {
    updateInvoiceData() {
      this.$emit('input', { ...this.invoiceDataInternal });
    },
    clearData() {
      this.invoiceDataInternal = {
        name: '',
        taxNumber: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      };
      this.updateInvoiceData();
    },
    async getCountries() {
      this.countriesLoader = true;
      try {
        const response = await this.axios.get('/api/lists/countries');
        if (response.status === 200 && response.data) {
          this.countries = response.data;
        }
      } catch (error) {
        this.showSnackbar('error', this.$t('common_error_occurred'));
      } finally {
        this.countriesLoader = false;
      }
    },
  },
};
</script>
