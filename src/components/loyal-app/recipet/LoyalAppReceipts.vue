<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        sm="12"
        md="12"
      >
        <v-row>
          <v-col
            cols="12"
            sm="12"
            md="6"
          >
            <date-range-picker
              key="dateRange"
              ref="dateRange"
              prepend-icon="mdi-calendar-range"
              :show-presets="true"
              :show-custom="true"
              :disabled="loader"
              start-preset="currentMonth"
              @reload-transaction-list="onDateRangeChange"
            />
          </v-col>
          <v-col
            cols="12"
            sm="12"
            md="6"
            class="pt-md-8 px-3 pt-0"
          >
            <v-layout
              justify-end
              align-center
            >
              <btn-refresh
                class="mt-0"
                size="small"
                :disabled="loader"
                @click="getData"
              />
              <report-create-modal
                btn-class="ml-2"
                :params="exportAsyncParams"
                :disabled="loader"
                :preset="filtering.search.dateValue"
              />
            </v-layout>
          </v-col>
        </v-row>
      </v-col>
      <v-col
        cols="12"
        class="px-0"
      >
        <div
          v-if="loader"
          class="loader-background"
        />
        <v-data-table
          v-resize="onResize"
          mobile-breakpoint="0"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :loading="loader"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
          :footer-props="filtering.footerProps"
        >
          <template #progress>
            <div class="text-center">
              <v-progress-circular
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <template #item="{ item, expand, isExpanded }">
            <template v-if="!loader">
              <tr @click="expand(!isExpanded)">
                <td class="text-sm-start hidden-xs-only border-right">
                  {{ item.id }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up">
                  {{ item.time }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up border-right">
                  {{ item.mobile_payment_confirmed_timestamp }}
                </td>
                <td class="text-sm-center hidden-sm-and-down md-and-up">
                  {{ item.number }}
                </td>
                <td class="text-sm-center hidden-sm-and-down md-and-up border-right">
                  {{ item.jpk_id }}
                </td>
                <td class="text-sm-center hidden-sm-and-down md-and-up border-right">
                  {{ item.user_email }}
                </td>
                <td class="text-sm-end">
                  {{ item.gross|currencySymbol(item.currency_symbol) }}
                </td>
                <td class="text-sm-end hidden-sm-and-down md-and-up border-right">
                  {{ item.vat|currencySymbol(item.currency_symbol) }}
                </td>
                <td class="text-sm-center text-sm-end">
                  <v-layout
                    justify-end
                  >
                    <act-download
                      :url="`/api/gateway/wla-admin/receipt/${item.id}/download?app=${app}`"
                    />
                  </v-layout>
                </td>
              </tr>
            </template>
          </template>

          <template #expanded-item="{ headers, item }">
            <tr>
              <div
                v-if="windowWidth < 960"
                class="sm-and-down hidden-md-and-up"
              >
                <td colspan="8">
                  <v-card
                    flat
                    class="sm-and-down hidden-md-and-up"
                  >
                    <table class="expand-table">
                      <template
                        v-for="(header, index) in headers"
                      >
                        <tr
                          v-if="header.showInRowExpand"
                          :key="index"
                        >
                          <td><strong>{{ header.text }}</strong></td>
                          <td>
                            {{ item[header.value] }}
                          </td>
                        </tr>
                      </template>
                    </table>
                  </v-card>
                </td>
              </div>
            </tr>
          </template>

          <template #[`body.append`]>
            <template v-if="!loader">
              <tr class="table-summary">
                <td
                  class="hidden-xs-only text-start font-weight-bold"
                >
                  {{ $t('turnover.table.total') }}
                </td>
                <td class="hidden-md-and-down md-and-up text-sm-center" />
                <td class="hidden-md-and-down md-and-up text-sm-center" />
                <td class="hidden-sm-and-down md-and-up text-sm-center" />
                <td class="text-sm-center hidden-sm-and-down md-and-up" />
                <td class="text-sm-center hidden-sm-and-down md-and-up" />
                <td class="text-sm-end font-weight-bold">
                  <template v-if="dataTable.items.length !== 0">
                    {{
                      dataTable.sumGross|currencySymbol(siteCurrencySymbol)
                    }}
                  </template>
                  <template v-else>
                    -
                  </template>
                </td>
                <td class="text-sm-end hidden-sm-and-down md-and-up border-right font-weight-bold">
                  {{
                    dataTable.sumVat|currencySymbol(siteCurrencySymbol)
                  }}
                </td>
                <td />
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import DateRangePicker from '@components/common/DateRangePicker.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';

export default {
  components: {
    ActDownload,
    ReportCreateModal,
    DateRangePicker,
    BtnRefresh,
  },
  mixins: [
    ExportMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      autoLoadData: this.autoLoad,
      loader: false,
      filtering: {
        footerProps: {
          'items-per-page-options': [50, 100, 500, 1000],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        search: {
          dateFrom: '',
          dateTo: '',
        },
      },
      pagination: {
        page: 1,
        itemsPerPage: 50,
        sortBy: ['ctime'],
        sortDesc: [true],
      },
      dataTable: {
        sumGross: 0,
        sumVat: 0,
        totalItems: 0,
        headers: [
          {
            text: this.$t('common_id'),
            value: 'id',
            class: 'hidden-xs-only text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_createDate'),
            value: 'time',
            class: 'hidden-md-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_paymentDate'),
            value: 'mobile_payment_time',
            class: 'hidden-md-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_number'),
            value: 'number',
            class: 'hidden-sm-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: 'JPK ID',
            value: 'jpk_id',
            class: 'text-sm-center hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_username'),
            value: 'user_email',
            class: 'text-sm-center hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_value'),
            value: 'gross',
            class: 'text-sm-end',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_vat'),
            value: 'vat',
            class: 'text-sm-end hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('actions.actions'),
            value: 'actions',
            class: 'text-sm-end',
            sortable: false,
            showInRowExpand: false,
          },
        ],
        items: [
        ],
      },
    };
  },
  computed: {
    exportAsyncParams() {
      return {
        report: 'v2\\WlaReceiptsReport',
        ...this.getParams(),
      };
    },
    siteCurrencySymbol() {
      if (this.dataTable.items.length > 0) {
        return this.dataTable.items[0].currency_symbol ?? undefined;
      }

      return undefined;
    },
  },
  watch: {
    app() {
      this.getData();
      this.pagination.page = 1;
    },
    filtering: {
      handler() {
        this.getData();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if ((oldValue.sortDesc !== newValue.sortDesc
            || oldValue.page !== newValue.page
            || oldValue.itemsPerPage !== newValue.itemsPerPage
            || oldValue.sortBy !== newValue.sortBy)
          && this.autoLoadData) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          this.getData();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getParams() {
      return {
        startDate: this.filtering.search.dateFrom,
        endDate: this.filtering.search.dateTo,
        app: this.app,
      };
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    onDateRangeChange({
      from,
      to,
    }) {
      this.pagination.page = 1;
      this.filtering.search.dateFrom = from;
      this.filtering.search.dateTo = to;
    },
    getData() {
      this.loader = true;
      this.axios.get(
        '/api/loyalapp/receipts',
        {
          params: {
            ...this.getParams(),
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
          },
        },
      )
        .then((response) => {
          this.dataTable.items = response.data.data;
          this.dataTable.totalItems = response.data.total;
          this.dataTable.sumVat = response.data.totalSum.vat;
          this.dataTable.sumGross = response.data.totalSum.gross;
          this.loader = false;
        });
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

.expand-table {
  width: 100%
}
</style>
