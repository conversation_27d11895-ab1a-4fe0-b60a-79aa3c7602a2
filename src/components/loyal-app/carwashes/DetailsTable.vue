<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <div class="d-flex justify-space-between my-3">
      <h3 class="mb-3">
        {{ headerText }}
      </h3>
    </div>
    <v-simple-table
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('address') }}
            </td>
            <td
              class="text-left"
            >
              {{ item.address }}
            </td>
          </tr>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('last_mobile') }}
            </td>
            <td
              class="text-left"
            >
              {{ item.cw_api.last_mobile_online|formatDateDayTime }}
            </td>
          </tr>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('ip') }}
            </td>
            <td
              class="text-left"
            >
              {{ item.cw_api.ip ?? '-' }}
            </td>
          </tr>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('software') }}
            </td>
            <td
              class="text-left"
            >
              {{ item.cw_api.software }}
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <div class="d-flex justify-space-between my-3">
      <h3 class="my-3">
        {{ $t('common_owner') }}
      </h3>
    </div>
    <v-simple-table
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('table.company_name') }}
            </td>
            <td
              class="text-left"
            >
              {{ item.owner.client?.name }}
            </td>
          </tr>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('common_nip') }}
            </td>
            <td
              class="text-left"
            >
              {{ item.owner.client?.nip }}
            </td>
          </tr>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('loyalty-app.aggrement') }}
            </td>
            <td class="text-left">
              <a
                :href="item.owner.link"
                target="_blank"
              >{{ item.owner.aggrement }}</a>
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <div class="d-flex justify-space-between my-3">
      <h3 class="my-3">
        {{ $t('loyalApp_stands') }}
      </h3>
    </div>
    <v-simple-table
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('turnover.table.name') }}
            </td>
            <td
              class="border-right text-left"
            >
              {{ $t('common_standCode') }}
            </td>
            <td
              class="border-right text-center"
            >
              {{ $t('loyalApp_paymentEnabled') }}
            </td>
            <td
              class="text-center"
            >
              {{ $t('loyalApp_qrCode') }}
            </td>
          </tr>
          <tr
            v-for="[key, stand] in Object.entries(item.cw_api.stands)"
            :key="key"
          >
            <td
              class="border-right text-left"
            >
              <v-icon>
                <!-- w okresie przejsciowym beloyal inaczej zwraca source od mp-->
                {{ getIconForDeviceType(stand?.source.name ?? stand.source) }}
              </v-icon>
              {{ getTextForDeviceType(stand?.source.name ?? stand.source) }}
              {{ `#${stand.bay_id}` }}
            </td>
            <td
              class="border-right text-left"
            >
              <!-- w okresie przejsciowym beloyal inaczej zwraca stand_code od mp-->
              {{ stand.stand_code ?? stand.stand_id }}
            </td>
            <td
              class="border-right text-center"
            >
              <v-icon
                v-if="stand.mobile_enable"
                color="green darken-2"
              >
                mdi-check-circle-outline
              </v-icon>
              <v-icon
                v-else
                color="error"
              >
                mdi-close-circle-outline
              </v-icon>
            </td>
            <td>
              <div class="card-buttons">
                <act-download
                  :url="`/api/gateway/wla-admin/sticker/${stand.stand_code}?app=${app}`"
                />
              </div>
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <div class="d-flex justify-space-between my-3">
      <h3 class="my-3">
        {{ $t('loyalApp_carwashBasicData') }}
      </h3>
      <v-tooltip bottom>
        <template #activator="{ on, attrs }">
          <v-btn
            tile
            rounded
            x-small
            fab
            elevation="1"
            color="primary"
            class="mr-2 mt-2"
            v-bind="attrs"
            v-on="on"
            @click.stop
            @click.native="openModal(`editCarwashModal${item.id}`)"
          >
            <v-icon>
              mdi-pencil
            </v-icon>
          </v-btn>
        </template>
        <span>{{ $t('actions.edit') }}</span>
      </v-tooltip>
    </div>
    <div class="d-flex justify-space-between my-3">
      <h4>
        {{ $t('common_tableDescription') }}
      </h4>
    </div>
    <div>
      <v-simple-table
        dense
        class="elevation-3 py-2"
      >
        <template #default>
          <tbody>
            <tr>
              <td
                class="text-left"
              >
                {{ item.description ?? '-' }}
              </td>
            </tr>
          </tbody>
        </template>
      </v-simple-table>
    </div>
    <div class="d-flex justify-space-between my-3">
      <h4>
        {{ $t('loyalApp_cashback') }}
      </h4>
    </div>
    <v-simple-table
      dense
      class="elevation-3 py-2 my-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('loyalApp_cashback') }}
            </td>
            <td
              class="text-left"
            >
              {{ item.cashback }}
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-container
      fluid
      justify-center
    >
      <div
        class="flex-column"
        align="center"
        justify="center"
      >
        <img
          v-if="item.photo"
          :src="`data:image/png;base64,${item.photo}`"
          class="formLogo"
        >
      </div>
    </v-container>
    <carwash-edit-modal
      :ref="`editCarwashModal${item.id}`"
      :carwash-id="item.id"
      :carwash-data="item"
      :app="app"
      @update-fail="onFail()"
      @update-success="onSuccess()"
    />
  </v-card>
</template>

<script>
import i18n from '@/i18n';
import ActDownload from '@components/common/Action/ActDownload.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import CarwashEditModal from '@components/loyal-app/carwashes/CarwashEditModal.vue';

export default {
  name: 'DetailsTable',
  components: {
    ActDownload,
    CarwashEditModal,
  },
  mixins: [
    ExportMixin,
    SnackbarMixin,
  ],
  props: {
    data: {
      type: [Object, Array],
      required: true,
    },
    app: {
      type: String,
      default: null,
    },
    headerText: {
      type: String,
      default: () => i18n.t('dashboard.table-details'),
      required: false,
    },
  },
  data() {
    return {
      deviceIcons: {
        CAR_WASH: 'mdi-car-wash',
        VACUUM_CLEANER: 'mdi-auto-fix',
        DISTRIBUTOR: 'mdi-cup-water',
        MONEY_CHANGER: 'mdi-sync',
      },
      deviceText: {
        CAR_WASH: this.$t('fiscal_transactions.source.CAR_WASH'),
        VACUUM_CLEANER: this.$t('fiscal_transactions.source.VACUUM_CLEANER'),
        DISTRIBUTOR: this.$t('fiscal_transactions.source.DISTRIBUTOR'),
        MONEY_CHANGER: this.$t('fiscal_transactions.source.MONEY_CHANGER'),
      },
    };
  },
  computed: {
    item() {
      if (typeof this.data === 'undefined') {
        return [];
      }

      return this.data;
    },
  },
  methods: {
    onSuccess() {
      this.showSnackbar(
        'success',
        this.$t('common_success'),
      );
      this.$emit('refresh');
    },
    onFail() {
      this.showSnackbar(
        'error',
        this.$t('common_errorHeader'),
      );
    },
    getIconForDeviceType(deviceType) {
      if (deviceType in this.deviceIcons) {
        return this.deviceIcons[deviceType];
      }
      return 'mdi-help';
    },
    getTextForDeviceType(deviceType) {
      if (deviceType in this.deviceText) {
        return this.deviceText[deviceType];
      }
      return deviceType;
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
  },
};
</script>

<style lang="css" scoped>
  .card-buttons {
    display: flex;
    justify-content: flex-end;
  }

  .card-button {
    margin-right: 0.3rem;
  }
</style>
