<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        sm="12"
        md="12"
      >
        <v-row>
          <v-col
            cols="12"
            sm="12"
            md="4"
          >
            <text-search
              v-model="text"
              @input="getDataDebounced"
            />
          </v-col>
          <v-col
            md="4"
            sm="12"
            cols="12"
          >
            <multiselect
              v-model="filtering.status"
              :items="statusesDisabled"
              :label="filters.statuses.text"
              :prepend-icon="filters.statuses.icon"
              :return-array="true"
              :disabled="loader"
              unified
              dense
              allow-null
            />
          </v-col>
          <v-col
            cols="12"
            sm="12"
            md="4"
          >
            <date-range-picker
              key="dateRange"
              ref="dateRange"
              prepend-icon="mdi-calendar-range"
              :show-presets="true"
              :show-custom="true"
              :disabled="loader"
              start-preset="currentMonth"
              @reload-transaction-list="onDateRangeChange"
            />
          </v-col>
        </v-row>
        <v-row>
          <v-col
            cols="12"
            sm="12"
            class="pt-1 pb-1 px-3"
          >
            <v-layout
              row
              justify-end
              wrap
            >
              <v-col
                md="4"
                sm="12"
                cols="12"
                offset-md="7"
                class="display-flex align-centerpt-1 pb-1 pr-1 pl-1"
              >
                <v-layout
                  justify-end
                  align-center
                >
                  <btn-refresh
                    class="mt-0"
                    size="small"
                    :disabled="loader"
                    @click="getInvoicesList"
                  />
                  <report-create-modal
                    btn-class="ml-2"
                    :params="exportAsyncParams"
                    :disabled="loader"
                    :preset="filtering.search.dateValue"
                  />
                </v-layout>
              </v-col>
            </v-layout>
          </v-col>
        </v-row>
      </v-col>
      <v-col
        cols="12"
        class="px-0"
      >
        <div
          v-if="loader"
          class="loader-background"
        />
        <v-data-table
          v-resize="onResize"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="invoiceId"
          :loading="loader"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
          :footer-props="dataTable.footerProps"
        >
          <template #progress>
            <div class="text-center">
              <v-progress-circular
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <template #item="{ item, expand, isExpanded }">
            <template v-if="!loader">
              <tr @click="expand(!isExpanded)">
                <td class="text-sm-start hidden-xs-only">
                  {{ item.user_email }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up ">
                  {{ item.client_tax_number !== null ? item.client_tax_number : '-' }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up ">
                  {{ item.client_name !== null ? item.client_name : '-' }}
                </td>
                <td class="text-sm-center hidden-sm-and-down md-and-up">
                  {{ item.issuance_date|formatDateDay }}
                </td>
                <td class="text-sm-center hidden-sm-and-down md-and-up">
                  {{ item.payment_date|formatDateDay }}
                </td>
                <td class="text-sm-start">
                  <v-layout
                    justify-start
                  >
                    <invoice-status :status="item.status" />
                    <span class="pt-1 pl-1">
                      {{ item.number }}
                    </span>
                  </v-layout>
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up">
                  {{ item.price_excluded_tax|currencySymbol(item.currency_symbol) }}
                </td>
                <td class="text-sm-center hidden-sm-and-down md-and-up">
                  {{ item.price|currencySymbol(item.currency_symbol) }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up">
                  {{ item.tax_amount|currencySymbol(item.currency_symbol) }}
                </td>
                <td class="text-sm-center text-sm-end">
                  <v-layout
                    justify-end
                  >
                    <template v-if="item.status === 'waiting'">
                      <btn-confirm
                        small
                        class="pt-1"
                        :confirmed="false"
                        :confirm-hint-text="$t('loyalApp_confirmPaymentAndInvoie')"
                        @confirm="confirmPayment(item)"
                      />
                    </template>
                    <act-download
                      :url="`/api/gateway/wla-admin/invoice/${item.id}/download?app=${app}`"
                    />
                    <v-tooltip bottom>
                      <template #activator="{ on }">
                        <v-btn
                          class="ml-2"
                          x-small
                          tile
                          rounded
                          fab
                          elevation="1"
                          color="primary"
                          v-on="on"
                          @click.stop
                          @click.native="sendInvoiceEmail(item, app)"
                        >
                          <v-icon>mdi-email</v-icon>
                        </v-btn>
                      </template>
                      <span>{{ $t('actions.send_invoice') }}</span>
                    </v-tooltip>
                  </v-layout>
                </td>
              </tr>
            </template>
          </template>

          <template #expanded-item="{ headers, item }">
            <tr>
              <div
                v-if="windowWidth < 960"
                class="sm-and-down hidden-md-and-up"
              >
                <td colspan="8">
                  <v-card
                    flat
                    class="sm-and-down hidden-md-and-up"
                  >
                    <table class="expand-table">
                      <template
                        v-for="(header, index) in headers"
                      >
                        <tr
                          v-if="header.showInRowExpand"
                          :key="index"
                        >
                          <td><strong>{{ header.text }}</strong></td>
                          <td>
                            {{ item[header.value] }}
                          </td>
                        </tr>
                      </template>
                    </table>
                  </v-card>
                </td>
              </div>
            </tr>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import DateRangePicker from '@components/common/DateRangePicker.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import debounce from 'lodash/debounce';
import TextSearch from '@components/common/filters/TextSearch.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import BtnConfirm from '@components/common/BtnConfirm.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import { endOfToday, startOfToday, startOfYear } from 'date-fns';
import InvoiceStatus from '@components/domain/invoices/InvoiceStatus.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';

export default {
  components: {
    ActDownload,
    InvoiceStatus,
    ReportCreateModal,
    Multiselect,
    BtnRefresh,
    BtnConfirm,
    TextSearch,
    DateRangePicker,
  },
  mixins: [
    ExportMixin,
    SnackbarMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loader: false,
      exportItems: [
        {
          title: 'actions.export_csv',
          action: 'exportCsv',
          icon: 'get_app',
        },
        {
          title: 'actions.export_xlsx',
          action: 'exportXlsx',
          icon: 'description',
        },
      ],
      searchRules: [
        (value) => /^((?!%|\?|!|\)|\(|'|&).)*$/.test(value) || this.$t(
          'form.validation.invalid_value',
        ),
      ],
      filters: {
        statuses: {
          icons: {
            error: 'mdi-close-octagon-outline',
            confirmed: 'mdi-check-circle-outline',
            rejected: 'mdi-alert-outline',
            initiated: 'mdi-cached',
            timeout: 'mdi-clock-outline',
            waiting: 'mdi-cached',
            refunded: 'mdi-credit-card-refund-outline',
          },
          colors: {
            error: 'error',
            confirmed: 'green darken-2',
            rejected: 'error',
            initiated: 'progress',
            timeout: 'error',
            waiting: 'progress',
            refunded: 'warning',
          },
          texts: {
            error: this.$t('common_error'),
            confirmed: this.$t('common_paid'),
            rejected: this.$t('common_canceled'),
            initiated: this.$t('common_processing'),
            timeout: this.$t('common_timeout'),
            waiting: this.$t('common_processing'),
            refunded: this.$t('common_refund'),
          },
          text: this.$t('common_state'),
          icon: 'mdi-list-status',
        },
        issuers: {
          icons: {
            P24: 'mdi-bank',
            ESPAGO: 'mdi-credit-card',
          },
          text: this.$t('loyalApp_externalType'),
          icon: 'mdi-cash-multiple',
        },
      },
      text: null,
      possibleStatuses: [
      ],
      filtering: {
        status: [
        ],
        search: {
          dateFrom: '',
          dateTo: '',
        },
      },
      pagination: {
        page: 1,
        itemsPerPage: 25,
        sortBy: ['user_email'],
        sortDesc: [false],
      },
      dataTable: {
        totalItems: 0,
        footerProps: {
          'items-per-page-options': [10, 25, 50],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('common_username'),
            value: 'user_email',
            class: 'hidden-xs-only text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_taxNumber'),
            value: 'nip',
            class: 'hidden-md-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('table.company_name'),
            value: 'company_name',
            class: 'hidden-md-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_issuanceDate'),
            value: 'issuance_date',
            class: 'hidden-sm-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_paymentDate'),
            value: 'payment_date',
            class: 'text-sm-center hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_invoiceNumber'),
            value: 'number',
            class: 'text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_valueNet'),
            value: 'price',
            class: 'text-sm-center hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_valueGross'),
            value: 'price_excluded_tax',
            class: 'text-sm-center hidden-md-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_vatTax'),
            value: 'tax_amount',
            class: 'text-sm-center hidden-md-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('actions.actions'),
            value: 'actions',
            class: 'text-sm-end',
            sortable: false,
            showInRowExpand: false,
          },
        ],
        items: [],
      },
    };
  },
  computed: {
    exportAsyncParams() {
      return {
        report: 'v2\\WlaInvoicesReport',
        ...this.getParams(),
      };
    },
    statusesDisabled() {
      const statuses = this.possibleStatuses.map(
        (row) => ({
          text: this.filters.statuses.texts[row.name] ?? row.name,
          value: row.name,
          disabled: false,
          icon: this.filters.statuses.icons[row.name] ?? '',
        }),
      );

      return statuses;
    },
  },
  watch: {
    app() {
      this.getInvoicesList();
    },
    filtering: {
      handler() {
        this.pagination.page = 1;
        this.getInvoicesList();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.sortDesc !== newValue.sortDesc
          || oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage
          || oldValue.sortBy !== newValue.sortBy) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
            this.getInvoicesList(true);
            return;
          }
          this.getInvoicesList();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getInvoicesList();
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.pagination.page = 1;
      this.getInvoicesList();
    }, 1000);
  },
  methods: {
    getParams() {
      return {
        startDate: this.filtering.search.dateFrom,
        endDate: this.filtering.search.dateTo,
        app: this.app,
        search: this.text,
        status: this.getToggleStatusFiltering(),
      };
    },
    start() {
      return startOfYear(startOfToday());
    },
    end() {
      return endOfToday();
    },
    confirmPayment(
      item,
    ) {
      const url = `/api/loyalapp/invoice/${item.id}/confirm?app=${this.app}`;

      this.axios.post(url, {
        status: 'confirmed',
      })
        .then((response) => {
          if (response.status === 200) {
            this.getInvoicesList();
          }
        })
        .catch(() => {
          this.showSnackbar(
            'error',
            this.$t('common_errorHeader'),
          );
        });
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    sendInvoiceEmail(item) {
      this.axios.get(
        `/api/gateway/wla-admin/invoice/${item.id}/send`,
        {
          params: {
            app: this.app,
          },
        },
      ).then(
        (response) => {
          if (response.status === 200) {
            this.showSnackbar(
              'success',
              this.$t('common_success'),
            );
          }
        },
        () => {
          // on error
          this.showSnackbar(
            'error',
            this.$t('common_errorHeader'),
          );
        },
      );
    },
    getToggleStatusFiltering() {
      return this.filtering.status.length ? this.filtering.status.join(',') : '';
    },
    onDateRangeChange({
      from,
      to,
    }) {
      this.filtering.search.dateFrom = from;
      this.filtering.search.dateTo = to;
    },
    getInvoicesList() {
      this.loader = true;
      this.axios.get(
        '/api/loyalapp/invoices',
        {
          params: {
            ...this.getParams(),
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
          },
        },
      )
        .then((response) => {
          this.dataTable.items = response.data.data;
          this.dataTable.totalItems = response.data.total;
          this.possibleStatuses = response.data.filters.statuses;
          this.loader = false;
        })
        .catch(() => {
          this.showSnackbar(
            'error',
            this.$t('common_errorHeader'),
          );
        });
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

.expand-table {
  width: 100%
}
</style>
