<template>
  <v-btn
    icon
    text
    tile
    small
    dark
    :disabled="disabled"
    @click="$emit('click')"
  >
    <v-icon>
      mdi-close
    </v-icon>
  </v-btn>
</template>

<script>
export default {
  props: {
    color: {
      type: String,
      default: 'green',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'x-small',
    },
  },
};
</script>
