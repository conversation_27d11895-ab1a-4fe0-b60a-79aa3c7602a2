<template>
  <v-btn
    x-small
    tile
    rounded
    fab
    elevation="1"
    color="primary"
    class="ml-2 white--text"
    :disabled="disabled"
    :loading="loading"
    @click.native="$emit('click')"
  >
    <template v-if="text">
      {{ text }}
    </template>
    <v-icon>
      mdi-pencil
    </v-icon>
  </v-btn>
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      default: null,
      nullable: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
