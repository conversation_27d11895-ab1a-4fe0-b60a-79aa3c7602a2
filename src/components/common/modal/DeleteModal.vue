<template>
  <div>
    <btn-delete
      @click="openDialog()"
    />

    <v-dialog
      v-model="dialog"
      max-width="500"
    >
      <v-card>
        <v-card-title class="text-h5">
          {{ text }}
        </v-card-title>

        <v-card-actions>
          <v-spacer />
          <v-btn
            text
            color="primary"
            @click="dialog = false"
          >
            {{ $t('actions.cancel') }}
          </v-btn>
          <v-btn
            color="red"
            class="white--text text-center"
            @click="deleteAndCloseDialog()"
          >
            {{ $t('actions.delete') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import BtnDelete from '@components/common/button/BtnDelete.vue';

export default {
  components: {
    BtnDelete,
  },
  props: {
    url: {
      type: String,
      required: true,
    },
    text: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
    };
  },
  methods: {
    openDialog() {
      this.dialog = true;
    },
    deleteAndCloseDialog() {
      this.deleteAction();
      this.dialog = false;
    },
    deleteAction() {
      this.axios.delete(
        this.url,
      )
        .then((response) => {
          if (response.status === 200) {
            this.snackbar.showMessage(
              {
                content: this.$t('common_success'),
                color: 'success',
              },
            );
            this.$emit('success');
          }
        })
        .catch(() => {
          this.snackbar.showMessage(
            {
              content: this.$t('common_errorHeader'),
              color: 'error',
            },
          );
          this.$emit('error');
        });
    },
  },
};
</script>
