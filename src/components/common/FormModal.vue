<template>
  <div>
    <slot name="before" />
    <v-dialog
      v-model="show"
      max-width="800"
      :persistent="loader"
    >
      <v-card :loading="loader">
        <v-card-title class="title">
          <slot name="title">
            <span class="headline text-uppercase text-h5">{{ title }}</span>
            <v-spacer />
            <v-btn
              icon
              text
              tile
              small
              dark
              @click="close"
            >
              <v-icon>
                mdi-close
              </v-icon>
            </v-btn>
          </slot>
        </v-card-title>
        <v-card-text>
          <v-form
            ref="form"
            @submit="submit"
          >
            <slot />
          </v-form>
        </v-card-text>
        <v-card-actions>
          <slot name="actions">
            <v-btn
              v-if="clearable"
              color="secondary"
              text
              @click="clear"
            >
              {{ $t('actions.clear') }}
            </v-btn>
            <v-spacer />
            <btn-send
              :text="confirmButtonText"
              :loading="loading"
              @click="submit"
            />
          </slot>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import BtnSend from '@components/common/button/BtnSend.vue';
import ModalMixin from '@components/common/mixins/ModalMixin.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  name: 'FormModal',
  components: { BtnSend },
  mixins: [
    ModalMixin,
    SnackbarMixin,
  ],
  props: {
    confirmButtonText: {
      type: String,
      default: null,
      nullable: true,
    },
    title: {
      type: String,
      default: '',
    },
    url: {
      type: String,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    errorPrefix: {
      type: String,
      default: 'application.',
    },
  },
  data: () => ({
    loadingInternal: false,
  }),
  computed: {
    loader() {
      return this.loading || this.loadingInternal;
    },
  },
  mounted() {
    this.$emit('mounted');
  },
  methods: {
    validate() {
      return this.$refs.form.validate();
    },
    clear() {
      this.$refs.form.reset();
    },
    beforeSubmit() {
      this.$emit('beforeSubmit');
    },
    submit() {
      this.beforeSubmit();
      if (this.validate()) {
        this.loadingInternal = true;
        this.axios.post(this.url, new FormData(this.$refs.form.$el))
          // .then((r) => r.data)
          .then((data) => this.afterSubmitSuccess(data))
          .catch((e) => this.afterSubmitFailure(e))
          .finally(() => {
            this.loadingInternal = false;
          });
      }
    },
    afterSubmitFailure(e) {
      this.$emit('submitFail', e);
      this.showSnackbar(
        'error',
        e.response.data.message ? this.$t(`${this.errorPrefix}${e.response.data.message}`) : this.$t('common_errorHeader'),
      );
    },
    afterSubmitSuccess(data) {
      this.$emit('submit', data);

      if (data.error) {
        this.showSnackbar(
          'error',
          data.error,
        );
      } else {
        this.showSnackbar(
          'success',
          this.$t('common_actionSucced'),
        );
      }

      this.close();
      this.clear();
    },
  },
};
</script>
