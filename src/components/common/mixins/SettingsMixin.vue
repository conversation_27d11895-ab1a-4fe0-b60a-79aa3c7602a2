<script>

import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'SettingsMixin',
  data() {
    return {
      reportName: 'export',
    };
  },
  computed: {
    ...mapGetters({
      getSettings: 'settings/findByNamespace',
    }),
  },
  methods: {
    ...mapActions({
      modifySettings: 'settings/modifySettings',
    }),
    getSetts(namespace, defaultValue) {
      if (this.getSettings(namespace) === undefined) {
        return defaultValue;
      }

      return this.getSettings(namespace).value;
    },
    setSetts(namespace, newValue) {
      if (newValue !== null && newValue !== undefined) {
        this.modifySettings({ namespace, value: newValue });
      }
    },
  },
};
</script>
