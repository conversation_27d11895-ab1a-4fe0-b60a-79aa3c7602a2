<script>

import moment from 'moment';
import { mapGetters } from 'vuex';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  name: 'DataFetchMixin',
  mixins: [
    SnackbarMixin,
  ],
  data() {
    return {
      liveData: false,
      lastTimoutId: null,
      loader: true,
      lock: false,
      hasErrors: false,
      items: [],
      dataUrl: '',
      totalItems: -1,
      currencyObject: {},
      options: {},
      sums: {},
      pageSums: {},
      totalSums: {},
      dataFetchInterval: null,
      dataFetchIntervalTimeout: 5000,
      updatedAt: null,
    };
  },
  computed: {
    ...mapGetters({
      timezone: 'auth/userTimezone',
    }),
  },
  watch: {
    loader(val, oldVal) {
      if (val !== oldVal) {
        if (val) {
          this.$emit('startLoading');
        } else {
          this.$emit('stopLoading');
        }
      }
    },
    filtering: {
      handler(newFilter) {
        if (newFilter.dates.from === null && newFilter.dates.isLastCollection === false) {
          return;
        }
        this.setDataFetchInterval();
        this.setDataFetchDebounce();
        // this.getData();
      },
      deep: true,
    },
    liveData() {
      this.setDataFetchInterval();
    },
  },
  beforeMount() {
    this.setDataFetchInterval();
  },
  beforeDestroy() {
    clearInterval(this.dataFetchInterval);
  },
  methods: {
    parseApiResponseData(data) {
      // Implement in child component
      this.items = [...data];
    },
    afterItemsUpdate() {
      // Implement in child component
      return false;
    },
    getUrl() {
      return this.dataUrl;
    },
    getParams() {
      return this.params;
    },
    setDataFetchInterval() {
      clearInterval(this.dataFetchInterval);
      if (this.liveData) {
        this.dataFetchInterval = setInterval(this.getDataSilent, this.dataFetchIntervalTimeout);
      }
    },
    setDataFetchDebounce() {
      if (this.lock) {
        return;
      }
      clearTimeout(this.lastTimoutId);
      this.lastTimoutId = setTimeout(this.getData, 800);
    },
    getCurrencySymbol(currency) {
      return (0).toLocaleString(
        this.$i18n.locale,
        {
          style: 'currency',
          currency,
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        },
      )
        .replace(/\d/g, '')
        .trim();
    },
    fetch() {
      this.lock = true;
      const url = this.getUrl();
      const params = this.getParams();
      return this.axios.get(url, params);
    },
    afterFetchSuccess(response) {
      if (response.status === 200) {
        this.hasErrors = false;
        this.$emit('alert', false);
        this.loader = false;
        if (response.data.error) {
          throw response.data.message;
        }

        if ('currency' in response.data && response.data.currency !== null) {
          this.currencyObject = response.data.currency;
          // if (typeof response.data.currency === 'object') {
          //   window.currency = {
          //     code: response.data.currency.code,
          //     symbol: response.data.currency.symbol,
          //   };
          // } else {
          //   let symbol;
          //   if (response.data.currency === 'credit') {
          //     symbol = 'C';
          //   } else {
          //     symbol = this.getCurrencySymbol(response.data.currency);
          //   }
          //   window.currency = {
          //     code: response.data.currency,
          //     symbol,
          //   };
          // }
        }

        if ('total' in response.data && response.data.total !== null) {
          this.totalItems = response.data.total;
        }

        if ('sums' in response.data) {
          this.sums = response.data.sums;
          if ('countTotal' in response.data.sums) {
            this.totalItems = response.data.sums.countTotal;
          }
        }

        if ('pageSum' in response.data) {
          this.pageSums = response.data.pageSum;
        }

        if ('totalSum' in response.data) {
          this.totalSums = response.data.totalSum;
        }

        if ('meta' in response.data) {
          if ('total' in response.data.meta) {
            this.totalItems = response.data.total;
          }
          if ('filtering' in response.data.meta) {
            this.filteringOptions = {
              ...this.filteringOptions,
              ...response.data.meta.filtering,
            };
          }
        }

        if (typeof response.data === 'object' && !response.data.data) {
          this.items = [];
          return;
        }

        if (!Object.values(response.data.data).length) {
          this.hasErrors = true;
        }

        if (response.data.data) {
          this.parseApiResponseData(response.data.data);
        }
        this.afterItemsUpdate(response.data);
        this.updatedAt = moment.tz(moment(), this.timezone).format('YYYY-MM-DD HH:mm:ss');
      }
      this.lock = false;
    },
    afterFetchFailure(error) {
      let alertMessage = error;

      if (error.response.status === 500) {
        alertMessage = `${this.$t('common_errorHeader')}. ${this.$t('contact_message')}`;
      }

      this.hasErrors = true;
      this.$emit('alert', alertMessage, 'error');
      this.lock = false;

      this.showSnackbar('error', alertMessage);
    },
    getData() {
      if (this.lock) {
        return;
      }

      this.items = [];
      this.sums = {};
      this.pageSums = {};
      this.totalSums = {};
      this.afterItemsUpdate();
      this.loader = true;

      const promise = this.fetch();
      promise.then((response) => {
        this.afterFetchSuccess(response);
        this.loader = false;
      }).catch((error) => {
        this.afterFetchFailure(error);
        this.loader = false;
      });
    },
    getDataSilent() {
      if (this.lock) {
        return;
      }
      this.items = [];
      this.sums = {};
      this.pageSums = {};
      this.totalSums = {};

      const promise = this.fetch();
      promise.then((response) => {
        this.afterFetchSuccess(response);
      }).catch((error) => {
        this.afterFetchFailure(error);
      });
    },
  },
};

</script>
