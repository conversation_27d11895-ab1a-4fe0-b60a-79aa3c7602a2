<template>
  <div>
    <v-chart
      ref="chart"
      class="chart"
      :option="option"
      autoresize
      :loading="loader"
      :loading-options="{
        text: $t('common_loading'),
        color: '#48a7f2',
        lineWidth: 2,
      }"
      @legendselectchanged="(selected) => $emit('legendselectchanged', selected)"
    />
  </div>
</template>

<script>
import VChart from 'vue-echarts';

export default {
  name: 'ChartMixin',
  components: {
    VChart,
  },
  props: {
    loader: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    titleAlign: {
      type: String,
      default: 'left',
    },
    legend: {
      type: Array,
      default() {
        return [];
      },
    },
    legendOrientation: {
      type: String,
      default: 'horizontal',
    },
    legendAlign: {
      type: String,
      default: 'center',
    },
    legendVerticalAlign: {
      type: String,
      default: 'top',
    },
  },
};
</script>
