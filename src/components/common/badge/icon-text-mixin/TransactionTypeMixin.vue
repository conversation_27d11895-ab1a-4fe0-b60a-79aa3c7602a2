<script>
export default {
  name: 'TransactionMixin',
  data() {
    return {
      icons: {
        SUBTRACTION: 'mdi-trending-down',
        ADDITION: 'mdi-trending-up',
        ALIGNMENT: 'mdi-wrench',
        PROMOTION: 'mdi-sale',
        TOP_UP_CODE: 'mdi-card-plus',
        BLOCKADE: 'mdi-lock',
        default: 'mdi-info',
      },
      texts: {
        SUBTRACTION: this.$t('transactions.payment'),
        ADDITION: this.$t('transactions.topup'),
        ALIGNMENT: this.$t('transactions.balance_adjustment'),
        PROMOTION: this.$t('transactions.promotions'),
        BLOCKADE: this.$t('loyaltyCards_lockFund'),
        TOP_UP_CODE: this.$t('transactions.top-up-code'),
      },
    };
  },
  methods: {
    getPaymentTypeDetails(type) {
      if (type in this.icons) {
        return {
          icon: this.icons[type],
          text: this.texts[type],
        };
      }

      return {
        icon: this.icons.default,
        text: type,
      };
    },
  },
};
</script>
