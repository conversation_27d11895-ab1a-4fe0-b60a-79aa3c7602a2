<template>
  <div>
    <template v-if="tooltip">
      <v-tooltip bottom>
        <template #activator="{ on, attrs }">
          <v-icon
            :color="color"
            :small="small"
            v-bind="attrs"
            v-on="on"
          >
            {{ icons[type] }}
          </v-icon>
        </template>
        <span>
          {{ $t(`fiscal_transactions.source.${type}`) }} {{ stand ? `#${stand}` : '' }}
        </span>
      </v-tooltip>
    </template>
    <template v-else>
      <v-icon
        :small="small"
        :color="color"
      >
        {{ icons[type] }}
      </v-icon>
      {{ $t(`fiscal_transactions.source.${type}`) }} {{ stand ? `#${stand}` : '' }}
    </template>
  </div>
</template>

<script>
export default {
  name: 'DeviceTypeBadge',
  props: {
    deviceType: {
      type: String,
      default: null,
    },
    standId: {
      type: Number,
      default: null,
    },
    xSmall: {
      type: Boolean,
      default: true,
    },
    small: {
      type: <PERSON>olean,
      default: false,
    },
    color: {
      type: String,
      nullable: true,
      default: null,
    },
    iconClass: {
      type: String,
      default: 'mr-1',
    },
    tooltip: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      type: this.deviceType,
      stand: this.standId,
      icons: {
        CAR_WASH: 'mdi-car-wash',
        VACUUM_CLEANER: 'mdi-auto-fix',
        DISTRIBUTOR: 'mdi-cup-water',
        MONEY_CHANGER: 'mdi-cash-100',
        UNKNOWN: 'mdi-help',
        INTERNET: 'mdi-wifi',
        SCRIPT: 'mdi-dns',
      },
    };
  },
};
</script>
