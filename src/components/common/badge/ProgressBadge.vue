<template>
  <div>
    <template v-if="tooltip">
      <v-tooltip bottom>
        <template #activator="{ on, attrs }">
          <v-icon
            :small="small"
            :color="color"
            v-bind="attrs"
            v-on="on"
          >
            {{ getTopUpProgressIcon(val).icon }}
          </v-icon>
        </template>
        <span>
          {{ getTopUpProgressIcon(val).text }}
        </span>
      </v-tooltip>
    </template>
    <template v-else>
      <v-icon
        :small="small"
        :color="color"
      >
        {{ getTopUpProgressIcon(val).icon }}
      </v-icon>
      {{ getTopUpProgressIcon(val).text }}
    </template>
  </div>
</template>

<script>
export default {
  name: 'DeviceTypeBadge',
  props: {
    value: {
      type: String,
      default: null,
    },
    xSmall: {
      type: Boolean,
      default: true,
    },
    small: {
      type: Boolean,
      default: false,
    },
    color: {
      type: String,
      nullable: true,
      default: null,
    },
    iconClass: {
      type: String,
      default: 'mr-1',
    },
    tooltip: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      val: this.value,
      icons: {
        CANCELED: 'mdi-cancel',
        WAITING: 'mdi-timer-sand-empty',
        NOT_FULLY_REFILLED: 'mdi-sync',
        REFILLED: 'mdi-check-underline',
      },
      text: {
        CANCELED: this.$t('administration.subscription.status.canceled'),
        WAITING: this.$t('common_toSent'),
        NOT_FULLY_REFILLED: this.$t('common_notFullySent'),
        REFILLED: this.$t('common_sendToCard'),
      },
    };
  },
  methods: {
    getTopUpProgressIcon(val) {
      return {
        icon: this.icons[val] ?? 'question',
        text: this.text[val] ?? this.$t('unknown'),
      };
    },
  },
};
</script>
