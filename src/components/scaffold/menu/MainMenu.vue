<template>
  <div>
    <v-navigation-drawer
      v-model="drawer.state"
      :mini-variant.sync="drawer.mini"
      app
      width="251"
      :hide-overlay.sync="drawer.hideOverlay"
      mini-variant-width="70"
      :temporary.sync="drawer.temporary"
      class="mainnavigationDrawer"
    >
      <v-toolbar
        flat
        class="mainnavigationDrawer menu-bar top elevation-0"
        height="64"
      >
        <v-btn
          class="m-0"
          icon
          @click.stop="toggleDrawer()"
        >
          <v-icon v-if="!isDrawerOpen()">
            mdi-menu
          </v-icon>
          <v-icon v-if="isDrawerOpen()">
            mdi-close
          </v-icon>
        </v-btn>
        <img
          v-if="!drawer.mini"
          alt="logo"
          class="top-logo"
          src="@assets/logo-invert.png"
        >
      </v-toolbar>
      <v-list
        dense
        dark
      >
        <div
          v-for="(menuPosition, i) in menuPositions"
          :key="i"
          class="mx-auto white--text"
        >
          <v-list-item
            v-if="menuPosition.isDivider"
            :key="menuPosition.id"
          >
            <v-divider />
          </v-list-item>
          <v-list-group
            v-if="menuPosition.subPositions && !menuPosition.isDivider && menuPosition.show"
            :key="menuPosition.name"
            :group="menuPosition.group"
            no-action
            class="white--text"
            :prepend-icon="menuPosition.icon"
          >
            <template #activator>
              <v-list-item-title>
                {{ menuPosition.name }}
              </v-list-item-title>
            </template>
            <v-list-item
              v-for="subMenuPosition in menuPosition.subPositions"
              v-show="subMenuPosition.show"
              :key="subMenuPosition.id"
              :exact-path="subMenuPosition.exact ?? true"
              :to="{ name: subMenuPosition.url }"
              link
              class="white--text"
              @click="menuCurrentPosition = subMenuPosition.id"
            >
              <v-list-item-icon>
                <v-icon>
                  {{ subMenuPosition.icon }}
                </v-icon>
              </v-list-item-icon>
              <v-list-item-title>
                {{ subMenuPosition.name }}
              </v-list-item-title>
            </v-list-item>
          </v-list-group>
          <v-list-item
            v-if="!menuPosition.subPositions && !menuPosition.isDivider && menuPosition.show"
            :key="menuPosition.name"
            :to="{ name: menuPosition.url }"
            :exact-path="true"
            :class="menuPosition.url !== 'loyal_system' ? '' : 'pl-2'"
          >
            <v-list-item-icon>
              <v-icon
                v-if="menuPosition.url !== 'loyal_system'"
              >
                {{ menuPosition.icon }}
              </v-icon>
              <img
                v-else
                alt="logo"
                class="logo-menu"
                src="@assets/logobkfpay.png"
              >
            </v-list-item-icon>
            <v-list-item-title>
              {{ menuPosition.name }}
            </v-list-item-title>
          </v-list-item>
        </div>
      </v-list>
    </v-navigation-drawer>
    <v-toolbar
      tabs
      class="elevation-3 topmainbar"
      fixed
    >
      <v-btn
        v-if="termsOfUseAccepted"
        class="m-0"
        icon
        @click.stop="toggleDrawer()"
      >
        <v-icon
          v-if="!isDrawerOpen()"
          color="black"
        >
          mdi-menu
        </v-icon>
        <v-icon
          v-if="isDrawerOpen()"
          color="black"
        >
          mdi-close
        </v-icon>
      </v-btn>
      <v-toolbar-title>
        <img
          alt="brand-logo"
          :class="`top-logo ${drawer.mini ? 'pl-5' : ''}`"
          src="@assets/logo.png"
        >
      </v-toolbar-title>
      <v-spacer />
      <span class="subscription-type">
        {{ currentSubscription }}
      </span>
      <messages-menu />
      <user-menu :user="user" />
    </v-toolbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import UserMenu from '@components/scaffold/menu/UserMenu.vue';
import MessagesMenu from '@components/scaffold/menu/messages/MessagesMenu.vue';

export default {
  components: {
    UserMenu, MessagesMenu,
  },
  data() {
    return {
      common: {},
      termsOfUseAccepted: true,
      cmBrandLogo: '',
      drawer: {
        hideOverlay: true,
        state: false,
        mini: true,
        temporary: false,
      },
      menuCurrentPosition: 1,

    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/getUser',
      roles: 'auth/getRoles',
      hasRole: 'auth/hasRole',
      canAccess: 'auth/canAccess',
      currentSubscription: 'auth/subscriptionType',
    }),
    menuPositions() {
      return [
        {
          id: '0',
          name: this.$t('menu_cmdashboard'),
          icon: 'mdi-view-dashboard',
          url: 'dashboard',
          show: true,
        },
        {
          id: '2',
          name: this.$t('menu_finance'),
          icon: 'mdi-cash',
          group: '/finance',
          active: false,
          show: this.hasRole('ROLE_CM_FINANCE')
            && this.hasRole('ROLE_CM'),
          subPositions: [
            {
              id: '2_0',
              name: this.$t('common_financeTurnover'),
              icon: 'mdi-chart-bar',
              url: 'finance_turnover',
              show: this.hasRole('ROLE_CM_FINANCE')
                && this.hasRole('ROLE_SUBSCRIPTION_BASIC'),
            },
            {
              id: '2_1',
              name: this.$t('common_moneycollect'),
              icon: 'mdi-bank',
              url: 'finance_moneycollect',
              show: this.hasRole('ROLE_CM_FINANCE')
                && this.hasRole('ROLE_SUBSCRIPTION_BASIC'),
            },
            {
              id: '2_2',
              name: this.$t('common_financeMobilePayments'),
              icon: 'mdi-cellphone',
              url: 'finance_mobile_payments',
              show: this.hasRole('ROLE_CM_FINANCE'),
            },
            {
              id: '2_3',
              name: this.$t('common_total'),
              icon: 'mdi-chart-line',
              url: 'finance_programsusage',
              show: this.hasRole('ROLE_CM_FINANCE')
                && this.hasRole('ROLE_SUBSCRIPTION_BASIC'),
            },
            {
              id: '2_4',
              name: this.$t('menu.finance-fiscaltransactions'),
              icon: 'mdi-cash-register',
              url: 'finance_fiscaltransactions',
              show: this.hasRole('ROLE_CM_FINANCE')
                && this.hasRole('ROLE_SUBSCRIPTION_PREMIUM'),
            },
            {
              id: '2_5',
              name: this.$t('common_financeCarwashRates'),
              icon: 'mdi-currency-usd',
              url: 'finance_carwash_rates',
              show: this.hasRole('ROLE_CM_FINANCE')
                && this.hasRole('ROLE_SUBSCRIPTION_PREMIUM'),
            },
            {
              id: '2_6',
              name: this.$t('common_reports'),
              icon: 'mdi-group',
              url: 'finance_reports',
              show: this.hasRole('ROLE_CM_FINANCE')
                && this.hasRole('ROLE_SUBSCRIPTION_BASIC'),
            },
          ],
        },
        {
          id: '3',
          key: 'loyal_system',
          active: true,
          name: this.$t('common_loyalsystem'),
          icon: 'mdi-tag-heart',
          url: 'loyal_system',
          show: this.canAccess('loyalty', 'cards'),
        },
        {
          id: '4',
          name: this.$t('common_loyalAppManager'),
          icon: 'mdi-cellphone-wireless',
          url: 'loyal_app',
          show: this.hasRole('ROLE_CM_LOYAL_APP'),
        },
        {
          id: '5',
          name: this.$t('common_processData'),
          icon: 'mdi-thermometer-lines',
          url: 'process_data',
          show: this.hasRole('ROLE_CM_ALARMS_AND_TECHNICAL_DATA'),
        },
        {
          id: '6',
          name: this.$t('common_users'),
          icon: 'mdi-account-group',
          url: 'users',
          show: this.canAccess('users', 'enable'),
        },
        {
          id: '8',
          name: this.$t('common_service'),
          icon: 'mdi-hammer-wrench',
          url: 'service',
          show: this.hasRole('ROLE_CM_OWNER') || this.hasRole('ROLE_CM_SERVICE'),
        },
        // {
        //   id: '9',
        //   name: this.$t('menu_support'),
        //   icon: 'mdi-frequently-asked-questions',
        //   url: 'contact',
        //   show: this.hasRole('ROLE_CM'),
        // },
        {
          id: '10',
          name: `${this.$t('common_companyData')}`,
          icon: 'mdi-briefcase',
          url: 'invoice_company_data',
          show: this.canAccess('subscriber', 'read'),
        },
        {
          id: '11',
          name: `${this.$t('common_subscription')}`,
          icon: 'mdi-cash',
          url: 'subscription',
          show: this.hasRole('ROLE_CM_OWNER'),
        },
        {
          id: '12',
          name: this.$t('menu_administration'),
          icon: 'mdi-wrench',
          group: '/admin',
          active: false,
          show: this.hasRole('ROLE_CM_ADMINISTRATION'),
          subPositions: [
            {
              id: '12_0',
              name: this.$t('common_administrationCarwashes'),
              icon: 'mdi-car-wash',
              url: 'admin_carwashes',
              exact: false,
              show: this.hasRole('ROLE_CM_ADMINISTRATION'),
            },
            {
              id: '12_1',
              name: this.$t('menu_administrationUsers'),
              icon: 'mdi-account-multiple',
              url: 'admin_users',
              exact: false,
              show: this.hasRole('ROLE_CM_ADMINISTRATION'),
            },
            {
              id: '12_2',
              name: this.$t('menu_subscribers'),
              icon: 'mdi-invoice-multiple-outline',
              url: 'admin_subscribers',
              exact: false,
              show: this.hasRole('ROLE_CM_ADMINISTRATION'),
            },
            {
              id: '12_3',
              name: this.$t('menu_allSubscription'),
              icon: 'mdi-cash',
              url: 'subscriptions_all',
              exact: false,
              show: this.hasRole('ROLE_CM_ADMINISTRATION'),
            },
          ],
        },
        {
          id: '13',
          name: 'Predictive maintenance', // TODO: tłumaczenie,
          icon: 'mdi-tools',
          group: '/predictive_maintenance',
          active: false,
          show: this.hasRole('ROLE_SUPERADMIN'),
          subPositions: [
            {
              id: '13_0',
              name: 'Aktualne problemy',
              icon: 'mdi-alarm',
              url: 'issues',
              exact: false,
              show: this.hasRole('ROLE_SUPERADMIN'),
            },
            {
              id: '13_1',
              name: 'Wszystkie problemy',
              icon: 'mdi-list-box',
              url: 'all_issues',
              exact: false,
              show: this.hasRole('ROLE_SUPERADMIN'),
            },
            {
              id: '13_2',
              name: 'Aktywność',
              icon: 'mdi-account-supervisor',
              url: 'activity',
              exact: false,
              show: this.hasRole('ROLE_SUPERADMIN'),
            },
          ],
        },
        {
          id: '14',
          name: 'Usterka', // TODO: tłumaczenie,
          icon: 'mdi-hammer-screwdriver',
          group: '/service_new',
          active: false,
          show: this.hasRole('ROLE_SUPERADMIN'),
          subPositions: [
            {
              id: '14_0',
              name: 'Dashboard',
              icon: 'mdi-view-dashboard',
              url: 'service_dashboard',
              exact: false,
              show: this.hasRole('ROLE_SUPERADMIN'),
            },
            {
              id: '14_1',
              name: 'Urządzenia',
              icon: 'mdi-devices',
              url: 'service_devices',
              exact: false,
              show: this.hasRole('ROLE_SUPERADMIN'),
            },
            {
              id: '14_2',
              name: 'Klienci',
              icon: 'mdi-domain',
              url: 'service_clients',
              exact: false,
              show: this.hasRole('ROLE_SUPERADMIN'),
            },
            {
              id: '14_3',
              name: 'Zgłoszenia',
              icon: 'mdi-alert',
              url: 'service_issues',
              exact: false,
              show: this.hasRole('ROLE_SUPERADMIN'),
            },
            {
              id: '14_4',
              name: 'Zadania',
              icon: 'mdi-checkbox-multiple-marked',
              url: 'service_tasks',
              exact: false,
              show: this.hasRole('ROLE_SUPERADMIN'),
            },
            {
              id: '14_5',
              name: 'Wizyty',
              icon: 'mdi-car-cog',
              url: 'service_visits',
              exact: false,
              show: this.hasRole('ROLE_SUPERADMIN'),
            },
            {
              id: '14_6',
              name: 'Użytkownicy',
              icon: 'mdi-account',
              url: 'service_users',
              exact: false,
              show: this.hasRole('ROLE_SUPERADMIN'),
            },
          ],
        },
      ];
    },
  },
  mounted() {
    this.initDrawer();
    if (this.termsOfUseAccepted) {
      //  menuPositions
      let menuPositionName = this.$router.currentRoute.name;
      if (menuPositionName === undefined) {
        menuPositionName = 'loyal_system';
      }
      const menuPositionFromRouter = this.menuPositions.find(
        (menuPosition) => menuPosition.url === menuPositionName,
      );
      if (menuPositionFromRouter) {
        this.menuCurrentPosition = menuPositionFromRouter.id;
      }
    } else {
      this.openProfileMenuItem({ modal: 'termsOfUse' });
    }
  },
  methods: {
    initDrawer() {
      if (this.isMobile) {
        this.drawer.mini = false;
        this.drawer.state = false;
        this.drawer.temporary = true;
        this.drawer.hideOverlay = false;
      } else {
        this.drawer.state = true;
        this.drawer.mini = false;
        this.drawer.temporary = false;
        this.drawer.hideOverlay = true;
      }
    },
    toggleDrawer() {
      if (this.isMobile) {
        this.drawer.hideOverlay = false;
        this.drawer.mini = false;
        this.drawer.state = !this.drawer.state;
      } else {
        this.drawer.hideOverlay = true;
        this.drawer.mini = !this.drawer.mini;
        this.drawer.state = true;
      }
    },
    isDrawerOpen() {
      if (this.isMobile) {
        return this.drawer.state;
      }
      return !this.drawer.mini;
    },
  },
};
</script>

<style scoped>
  .logo-menu {
    margin-top: 4px;
    height: 16px;
    margin-right: 24px;
  }

  .top-logo {
    height: 40px;
  }
  .v-application .primary--text {
    color: #ffffff !important;
  }

  .subscription-type {
    text-transform: uppercase;
    font-size: 1.1em;
    font-weight: 500;
    margin-left: 0.5em;
    color: #48a7f2;
  }
</style>
