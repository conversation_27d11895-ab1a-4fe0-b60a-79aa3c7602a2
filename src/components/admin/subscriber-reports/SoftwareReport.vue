<template>
  <v-container fluid>
    <software-table
      :loader="loader"
      :items="items"
    />
  </v-container>
</template>

<script>
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import SoftwareTable from '@components/admin/subscriber-reports/SoftwareTable.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  name: 'SoftwareReport',
  components: {
    SoftwareTable,
  },
  mixins: [
    DataFetchMixin,
    SnackbarMixin,
  ],
  props: {
    url: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      dataUrl: this.url,
    };
  },

  mounted() {
    this.getData();
  },

  methods: {
    parseApiResponseData(data) {
      this.items = data;
    },
    getParams() {
      return {
        params: {
          report: 'v2\\CarwashesSoftwareReport',
        },
      };
    },
    afterFetchFailure() {
      this.hasErrors = true;
      this.showSnackbar('error', this.$t('common_error_occurred'));
      this.lock = false;
    },
  },
};
</script>
