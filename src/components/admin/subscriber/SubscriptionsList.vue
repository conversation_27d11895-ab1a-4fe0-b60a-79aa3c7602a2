<template>
  <v-container fluid>
    <div class="d-flex justify-end">
      <v-btn
        class="mb-4"
        small
        color="primary"
        @click.stop
        @click.native="openModal('addSubscriptionModal')"
      >
        <v-icon left>
          mdi-plus
        </v-icon>
        {{ $t('admin_add') }}
      </v-btn>
    </div>
    <v-row>
      <v-col
        cols="12"
        class="text-sm-start"
      >
        <v-data-table
          mobile-breakpoint="0"
          :headers="headers"
          :items="data"
          :items-per-page="200"
          :loading="loading"
          dense
        >
          <template #item="{ item }">
            <tr>
              <td class="text-sm-start">
                {{ item.startDate|formatDateDay }}
              </td>
              <td class="text-sm-start">
                {{ item.endDate|formatDateDay }}
              </td>
              <td :class="`text-sm-start ${getStatusColor(item.status)}--text`">
                {{ getStatusText(item.status) }}
              </td>
              <custom-currency-symbol-cell
                :currency="item.currencySymbol"
                :value="item.grossValue"
              />
              <td class="text-sm-end">
                {{ item.vatTax ? item.vatTax.taxValue : '-' }} %
              </td>
              <td class="text-sm-start">
                {{ item.type }}
              </td>
              <td class="text-sm-start">
                {{ item.ctime|formatDateDayTimeWithSeconds }}
              </td>
              <td class="text-sm-start">
                {{ item.whoAddedEmail ? item.whoAddedEmail : '-' }}
              </td>
              <td class="text-sm-start">
                {{ item.comment ? item.comment : '-' }}
              </td>
              <td class="text-sm-end">
                <v-tooltip bottom>
                  <template #activator="{ on }">
                    <span
                      class="pr-2"
                      v-on="on"
                    >
                      <v-btn
                        x-small
                        tile
                        rounded
                        fab
                        elevation="1"
                        color="primary"
                        class="my-1 white--text"
                        @click.stop="openModal(`previewSubscriptionModal${item.id}`)"
                      >
                        <v-icon>mdi-information</v-icon>
                      </v-btn>
                    </span>
                  </template>
                  <span>
                    {{ $t('admin_information') }}
                  </span>
                </v-tooltip>
                <v-tooltip bottom>
                  <template #activator="{ on }">
                    <span
                      class="pr-2"
                      v-on="on"
                    >
                      <v-btn
                        x-small
                        :disabled="buttondConfirmDisabled(item)"
                        tile
                        rounded
                        fab
                        elevation="1"
                        color="green"
                        class="my-1 white--text"
                        @click.stop="openModal(`previewConfirmModal${item.id}`)"
                      >
                        <v-icon>mdi-check</v-icon>
                      </v-btn>
                    </span>
                  </template>
                  <span>
                    {{ $t('admin_confirm') }}
                  </span>
                </v-tooltip>
                <v-tooltip bottom>
                  <template #activator="{ on }">
                    <span v-on="on">
                      <v-btn
                        x-small
                        :disabled="buttondDisabled(item)"
                        tile
                        rounded
                        fab
                        elevation="1"
                        color="red"
                        class="my-1 white--text"
                        @click.stop="openDeleteDialog(item.id)"
                      >
                        <v-icon>mdi-close</v-icon>
                      </v-btn>
                    </span>
                  </template>
                  <span>
                    <template
                      v-if="item.status === 'canceled' || item.status === 'manually_canceled'"
                    >
                      {{ $t('admin_alreadyCancel') }}: {{ item.mtime }}
                    </template>
                    <template v-else-if="item.editable">
                      {{ $t('admin_cancel') }}
                    </template>
                    <template v-else>
                      {{ $t('admin_automaticPayment') }}
                      <br>
                      {{ $t('admin_manualCancelNotPossible') }}
                    </template>
                  </span>
                </v-tooltip>
              </td>
            </tr>
            <subscription-confirm-modal
              :ref="`previewConfirmModal${item.id}`"
              :on-success="fetchData"
              :subscription-id="item.id"
            />
            <subscription-preview-modal
              :ref="`previewSubscriptionModal${item.id}`"
              :on-success="fetchData"
              :subscription-id="item.id"
            />
          </template>
        </v-data-table>
      </v-col>
      <subscription-add-modal
        ref="addSubscriptionModal"
        :subscriber-id="subscriberId"
        :on-success="fetchData"
      />
      <v-row justify="center">
        <v-dialog
          v-model="deleteDialog"
          max-width="500"
        >
          <v-card>
            <v-card-title class="text-h5">
              {{ $t("subscriptions.delete-question") }}
            </v-card-title>

            <v-card-actions>
              <v-spacer />
              <v-btn
                text
                color="primary"
                @click="deleteDialog = false"
              >
                {{ $t('actions.cancel') }}
              </v-btn>
              <v-btn
                color="red"
                class="white--text text-center"
                @click="cancelSubscriptionAndCLoseDialog()"
              >
                {{ $t('actions.delete') }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-row>
    </v-row>
  </v-container>
</template>

<script>
import customCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import subscriptionAddModal from '../subscription/SubscriptionAddModal.vue';
import subscriptionPreviewModal from '../subscription/SubscriptionPreviewModal.vue';
import subscriptionConfirmModal from '../subscription/SubscriptionConfirmModal.vue';

export default {
  name: 'SubscriptionsList',

  components: {
    customCurrencySymbolCell,
    subscriptionAddModal,
    subscriptionConfirmModal,
    subscriptionPreviewModal,
  },

  props: {
    subscriberId: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      data: [],
      loading: false,
      headers: [
        {
          text: this.$t('common_startDate'),
          value: 'startDate',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('common_subscriptionsEnddate'),
          value: 'endDate',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('common_state'),
          value: 'status',
          width: '150',
          sortable: false,
        },
        {
          text: this.$t('common_price'),
          value: 'grossValue',
          class: 'text-sm-end',
          width: '120',
          sortable: false,
        },
        {
          text: this.$t('admin_vat'),
          value: 'vatTax',
          class: 'text-sm-end',
          width: '80',
          sortable: false,
        },
        {
          text: this.$t('common_type'),
          value: 'type',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('admin_added'),
          value: 'added',
          width: '200',
          sortable: false,
        },
        {
          text: this.$t('admin_whoAdded'),
          value: 'whoAddedEmail',
          width: '200',
          sortable: false,
        },
        {
          text: this.$t('common_comment'),
          value: 'comment',
          sortable: false,
        },
        {
          text: this.$t('actions.actions'),
          class: 'text-sm-end',
          sortable: false,
          width: '150',
        },
      ],
      deleteDialog: false,
    };
  },

  mounted() {
    this.fetchData();
  },

  methods: {
    async fetchData() {
      this.loading = true;
      const response = await this.axios.get(`/administration/subscriber/${this.subscriberId}/subscriptions`);
      this.data = response.data.items;
      this.loading = false;
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    buttondDisabled(item) {
      return item.status === 'canceled'
        || item.status === 'manually_canceled'
        || !item.editable;
    },
    buttondConfirmDisabled(item) {
      return item.status !== 'initiated_proforma'
        || !item.editable;
    },
    getStatusColor(status) {
      switch (status) {
        case 'manually_canceled': return 'red';
        case 'canceled': return 'red';
        case 'paid': return 'green';
        case 'initiated': return 'orange';
        case 'initiated_proforma': return 'orange';
        default: return 'black';
      }
    },
    getStatusText(status) {
      switch (status) {
        case 'manually_canceled': return this.$t('administration.subscription.status.manually_canceled');
        case 'canceled': return this.$t('administration.subscription.status.canceled');
        case 'paid': return this.$t('administration.subscription.status.paid');
        case 'initiated': return this.$t('administration.subscription.status.initiated');
        case 'initiated_proforma': return `${this.$t('administration.subscription.status.initiated')} - proforma`;
        default: return this.$t('administration.subscription.status.unknown');
      }
    },
    cancelSubscription(subscriptionId) {
      this.loading = true;
      this.axios.delete(
        `/administration/subscription/${subscriptionId}`,
      )
        .then(() => {
          this.loading = false;
          this.fetchData();
        });
    },
    cancelSubscriptionAndCLoseDialog() {
      this.cancelSubscription(this.subscriptionToDeleteId);
      this.deleteDialog = false;
    },
    openDeleteDialog(subscriptionToDeleteId) {
      this.subscriptionToDeleteId = subscriptionToDeleteId;
      this.deleteDialog = true;
    },
    confirm(subscriptionId) {
      this.loading = true;
      this.axios.post(
        `/administration/subscription/${subscriptionId}/confirm`,
      )
        .then(() => {
          this.loading = false;
          this.fetchData();
        });
    },
  },
};
</script>
