<template>
  <span>
    <v-tooltip bottom>
      <template #activator="{ on }">
        <span
          class="pr-2"
          v-on="on"
        >
          <v-btn
            x-small
            tile
            rounded
            fab
            elevation="1"
            color="primary"
            :disabled="disabled"
            @click="showModal"
          >
            <v-icon>mdi-file-chart</v-icon>
          </v-btn>
        </span>
      </template>
      <span>
        {{ $t('admin_subscriptionReport') }}
      </span>
    </v-tooltip>
    <v-dialog
      v-model="show"
      max-width="800"
    >
      <v-card>
        <v-card-title class="title">
          <slot name="title">
            <span class="headline text-uppercase text-h5">{{ $t('common_exportAsyncTitle') }}</span>
            <v-spacer />
            <v-btn
              icon
              text
              tile
              small
              dark
              @click.native="close"
            >
              <v-icon>
                mdi-close
              </v-icon>
            </v-btn>
          </slot>
        </v-card-title>
        <v-card-text>
          <div
            v-if="loading"
            class="text-center py-4"
          >
            <v-progress-circular
              class="circleProgress md-2"
              :size="90"
              :width="7"
              color="primary"
              indeterminate
            />
            <h3 class="mt-4">
              {{ $t('common_generating') }}
            </h3>
            <h4 class="mt-4">
              {{ $t('common_reportDownloadOnList') }}:
              <v-btn
                text
                color="primary"
                @click="$router.push('/finance/reports')"
              >
                {{ $t('common_reports') }}
              </v-btn>
            </h4>
          </div>
          <div
            v-if="!loading"
            class="mt-7"
          >
            <template
              v-if="errorText !== null"
            >
              <v-alert
                border="left"
                class="mt-5"
                text
                type="error"
              >
                {{ errorText }}
              </v-alert>
            </template>
            <template
              v-if="!showDownload"
            >
              <div>
                <v-text-field
                  v-model="title"
                  :label="$t('common_userTitle')"
                  autofocus
                  prepend-icon="mdi-mail"
                  required
                />
                <v-combobox
                  v-model="invoiceCopyEmail.model"
                  :items="invoiceCopyEmail.items"
                  :search-input.sync="invoiceCopyEmail.search"
                  prepend-icon="mdi-email-multiple"
                  hide-selected
                  :data-vv-as="$t('common_emailCopyEmail')"
                  :label="$t('common_emailCopyEmail')"
                  :error-messages="errors.collect('invoiceCopyEmail')"
                  :rules="rules.comboboxEmail"
                  multiple
                  small-chips
                  deletable-chips
                >
                  <template #no-data>
                    <v-list-item>
                      <v-list-item-content>
                        <v-list-item-title>
                          {{ $t('common_pressEnterToAddNew') }}
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                </v-combobox>
              </div>
              <div
                class="text-center py-4"
              >
                <v-btn
                  color="primary"
                  @click="generateReport"
                >
                  <v-icon>
                    mdi-cached
                  </v-icon>
                  {{ $t('common_exportAsyncTitle') }}
                </v-btn>
              </div>
            </template>
            <div
              v-else
              class="text-center py-4"
            >
              <h3 class="py-4">
                {{ $t('common_reportReady') }}
              </h3>
              <v-btn
                class="mx-2"
                @click.native="clearReportGenerator"
              >
                {{ $t('common_clearReport') }}
              </v-btn>
              <v-btn
                class="mx-2"
                color="primary"
                @click.native="downloadReport"
              >
                {{ $t('common_downloadReport') }}
              </v-btn>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </span>
</template>

<script>
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'ExportSubscriptionsAsyncModal',
  mixins: [
    ExportMixin,
    SnackbarMixin,
  ],
  props: {
    params: {
      type: Object,
      required: true,
    },
    types: {
      type: Array,
      required: false,
      default: () => (
        [
          // {
          //   value: 'export_xlsx',
          //   title: 'actions.export_xlsx',
          //   filetype: 'xlsx',
          //   icon: 'mdi-file-excel',
          //   filename: 'test.xlsx',
          //   params: {
          //     ext: 'xlsx',
          //   },
          // },
          {
            value: 'export_pdf',
            title: 'actions.export_pdf',
            filetype: 'pdf',
            icon: 'mdi-file',
            filename: 'test.pdf',
            params: {
              ext: 'pdf',
            },
          },
        ]
      ),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    btnClass: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      invoiceCopyEmail: {
        model: [],
        items: [],
        search: null,
      },
      rules: {
        comboboxEmail: [(v) => /(((^(([^<>()[\]\\.,;:\s@']+(\.[^<>()\\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))(,{1}((([^<>()[\]\\.,;:\s@']+(\.[^<>()\\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))))*)|(^$))$/.test(v) || 'E-mail must be valid'],
      },
      title: null,
      selectedExportItem: null,
      loading: false,
      errorText: null,
      resourceId: null,
      resourceStatus: null,
      intervalId: null,
      fileType: null,
      fileName: null,
      show: false,
    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/getUser',
    }),
    itemsAsync() {
      return this.types.map(
        (type) => ({
          ...type,
          title: this.$t(type.title),
          params: {
            ...this.params,
            ...type.params,
          },
        }),
      );
    },
    showDownload() {
      return this.resourceStatus === 'DONE';
    },
  },
  mounted() {
    this.selectedExportItem = this.itemsAsync[0].value;
    this.invoiceCopyEmail.model = [
      this.user.email,
    ];
  },
  beforeDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  },
  methods: {
    generateReport() {
      const reportEntity = this.itemsAsync.find((item) => item.value === this.selectedExportItem);

      this.generateAsyncResource(reportEntity);
    },
    downloadReport() {
      const url = `/api/report/${this.resourceId}/download`;
      const filename = this.fileName;
      this.onExport({
        url,
        filename,
      });
    },
    generateAsyncResource(exportItem) {
      this.resourceStatus = 'initialized';
      this.loading = true;

      let invoiceCopyEmail = this.invoiceCopyEmail.model.join(',');
      if (invoiceCopyEmail === '') {
        invoiceCopyEmail = null;
      }

      const subscriptionId = exportItem.params.id;

      this.axios.post(
        `/administration/subscription/${subscriptionId}/report`,
        {},
        {
          params: {
            email: invoiceCopyEmail,
            title: this.title !== '' ? this.title : null,
            ...exportItem.params,
          },
        },
      )
        .then((response) => {
          this.fileName = exportItem.filename;
          this.fileType = exportItem.filetype;
          this.resourceId = response.data.id;
          this.checkResourceStatus(this.resourceId);
        })
        .catch(() => {
          this.errorText = `${this.$t('common_canotGenerateReport')}`;

          this.resourceStatus = 'Błąd podczas generowania zasobu.';
          this.loading = false;
        });
    },
    checkResourceStatus(resourceId) {
      this.intervalId = setInterval(() => {
        this.axios.get(`/api/report/${resourceId}`)
          .then((response) => {
            this.resourceStatus = response.data.status;
            if (response.status === 200) {
              this.fileName = response.data.fileName;
              clearInterval(this.intervalId);
              this.loading = false;
            }
          })
          .catch(() => {
            this.errorText = `${this.$t('common_canotGenerateReport')}`;
            this.loading = false;
            clearInterval(this.intervalId);
            // this.close();
          });
      }, 1000); // Odpytywanie co 1 sekunde
    },
    showModal() {
      this.show = true;
    },
    clearReportGenerator() {
      clearInterval(this.intervalId);
      this.selectedExportItem = this.itemsAsync[0].value;
      this.resourceId = null;
      this.resourceStatus = null;
      this.intervalId = null;
      this.errorText = null;
      this.loading = false;
    },
    close() {
      this.show = false;
      this.clearReportGenerator();
    },
  },
};
</script>
