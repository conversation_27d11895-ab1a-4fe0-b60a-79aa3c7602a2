<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        md="4"
        sm="12"
        cols="12"
        class="py-1"
      >
        <text-search
          v-model="search"
          :disabled="loader"
          @input="getDataDebounced"
        />
      </v-col>
      <v-col
        md="4"
        sm="12"
        cols="12"
        class="py-1"
      >
        <multiselect
          v-model="statuses"
          :items="statusItems"
          :label="$t('common_state')"
          prepend-icon="mdi-cash-check"
          :disabled="loader"
          unified
          allow-null
        />
      </v-col>
      <v-col
        md="4"
        sm="12"
        cols="12"
        class="py-1"
      >
        <date-range-picker
          key="dateRangeSUbscription"
          ref="dateRangeSUbscription"
          :show-presets="true"
          start-preset="currentMonth"
          @reload-transaction-list="onDateRangeChange"
        />
      </v-col>
      <v-col
        cols="12"
        class="text-sm-start"
      >
        <v-data-table
          :footer-props="dataTable.footerProps"
          :headers="headers"
          :items="subscriptionData"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
          dense
          item-key="id"
          mobile-breakpoint="0"
        >
          <template #item="{ item }">
            <tr>
              <td class="text-sm-start">
                {{ item.id }}
              </td>
              <td class="text-sm-start">
                {{ item.ctime|formatDateDayTimeWithSeconds }}
              </td>
              <td class="text-sm-start">
                {{ item.subscriber.name }}
              </td>
              <td class="text-sm-start">
                {{ item.startDate|formatDateDay }}
              </td>
              <td class="text-sm-start">
                {{ item.endDate|formatDateDay }}
              </td>
              <td class="text-sm-start">
                {{ item.type }}
              </td>
              <td :class="`text-sm-start ${getStatusColor(item.status)}--text`">
                {{ getStatusText(item.status) }}
              </td>
              <custom-currency-symbol-cell
                :currency="item.currencySymbol"
                :value="item.grossValue"
              />
              <td class="text-sm-end">
                {{ item.vatTax ? item.vatTax.taxValue : '-' }} %
              </td>
              <td class="text-sm-start">
                {{ item.whoAddedEmail ? item.whoAddedEmail : '-' }}
              </td>
              <td class="text-sm-start">
                {{ item.comment ? item.comment : '-' }}
              </td>
              <td class="text-sm-end justify-end">
                <export-subscriptions-async-modal
                  :ref="`exportSubscriptionModal${item.id}`"
                  :params="exportAsyncParams(item)"
                />
                <v-tooltip bottom>
                  <template #activator="{ on }">
                    <span
                      class="pr-2"
                      v-on="on"
                    >
                      <v-btn
                        x-small
                        tile
                        rounded
                        fab
                        elevation="1"
                        color="primary"
                        class="my-1 white--text"
                        @click.stop="openModal(`previewSubscriptionModal${item.id}`)"
                      >
                        <v-icon>mdi-information</v-icon>
                      </v-btn>
                    </span>
                  </template>
                  <span>
                    {{ $t('admin_information') }}
                  </span>
                </v-tooltip>
                <v-tooltip bottom>
                  <template #activator="{ on }">
                    <span
                      class="pr-2"
                      v-on="on"
                    >
                      <v-btn
                        x-small
                        :disabled="buttondConfirmDisabled(item)"
                        tile
                        rounded
                        fab
                        elevation="1"
                        color="green"
                        class="my-1 white--text"
                        @click.stop="openModal(`previewConfirmModal${item.id}`)"
                      >
                        <v-icon>mdi-check</v-icon>
                      </v-btn>
                    </span>
                  </template>
                  <span>
                    {{ $t('admin_confirm') }}
                  </span>
                </v-tooltip>
                <v-tooltip bottom>
                  <template #activator="{ on }">
                    <span v-on="on">
                      <v-btn
                        x-small
                        :disabled="buttondDisabled(item)"
                        tile
                        rounded
                        fab
                        elevation="1"
                        color="red"
                        class="my-1 white--text"
                        @click.stop="openDeleteDialog(item.id)"
                      >
                        <v-icon>mdi-close</v-icon>
                      </v-btn>
                    </span>
                  </template>
                  <span>
                    <template
                      v-if="item.status === 'canceled' || item.status === 'manually_canceled'"
                    >
                      {{ $t('admin_alreadyCancel') }}: {{ item.mtime }}
                    </template>
                    <template v-else-if="item.editable">
                      {{ $t('admin_cancel') }}
                    </template>
                    <template v-else>
                      {{ $t('admin_automaticPayment') }}
                      <br>
                      {{ $t('admin_manualCancelNotPossible') }}
                    </template>
                  </span>
                </v-tooltip>
              </td>
            </tr>
            <subscription-confirm-modal
              :ref="`previewConfirmModal${item.id}`"
              :on-success="getSubscriptionDetails"
              :subscription-id="item.id"
            />
            <subscription-preview-modal
              :ref="`previewSubscriptionModal${item.id}`"
              :on-success="getSubscriptionDetails"
              :subscription-id="item.id"
            />
          </template>
        </v-data-table>
      </v-col>
      <subscription-add-modal
        ref="addSubscriptionModal"
        :on-success="getSubscriptionDetails"
      />
      <v-row justify="center">
        <v-dialog
          v-model="deleteDialog"
          max-width="500"
        >
          <v-card>
            <v-card-title class="text-h5">
              {{ $t("subscriptions.delete-question") }}
            </v-card-title>

            <v-card-actions>
              <v-spacer />
              <v-btn
                text
                color="primary"
                @click="deleteDialog = false"
              >
                {{ $t('actions.cancel') }}
              </v-btn>
              <v-btn
                color="red"
                class="white--text text-center"
                @click="cancelSubscriptionAndCLoseDialog()"
              >
                {{ $t('actions.delete') }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-row>
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import TextSearch from '@components/common/filters/TextSearch.vue';
import customCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import exportSubscriptionsAsyncModal
  from '@components/admin/allSubscription/ExportSubscriptionsAsyncModal.vue';

import intersection from 'lodash/intersection';
import Multiselect from '@components/common/filters/Multiselect.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import subscriptionAddModal from '../subscription/SubscriptionAddModal.vue';
import subscriptionPreviewModal from '../subscription/SubscriptionPreviewModal.vue';
import subscriptionConfirmModal from '../subscription/SubscriptionConfirmModal.vue';

export default {
  name: 'UserSubscriptionList',
  components: {
    TextSearch,
    customCurrencySymbolCell,
    DateRangePicker,
    Multiselect,
    subscriptionAddModal,
    subscriptionConfirmModal,
    subscriptionPreviewModal,
    exportSubscriptionsAsyncModal,
  },
  mixins: [
    ExportMixin,
    SnackbarMixin,
  ],
  props: {
    basicData: {
      type: [Array, Object],
      default: () => [],
    },
  },
  data() {
    return {
      loader: true,
      statuses: {
      },
      search: null,
      filtering: {
        search: {
          dateFrom: null,
          dateTo: null,
        },
      },
      // discount: this.basicData.discount,
      deleteDialog: false,
      previewSubscription: null,
      subscriptionToDeleteId: null,
      subscriptionData: [],
      dataTable: {
        totalItems: 0,
        footerProps: {
          'items-per-page-options': [5, 10, 15, 25],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
      },
      possibleStatuses: [
      ],
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['startDate'],
        sortDesc: [true],
      },
      headers: [
        {
          text: this.$t('common_id'),
          value: 'id',
          sortable: false,
          width: '50',
        },
        {
          text: this.$t('admin_added'),
          value: 'added',
          sortable: false,
          width: '150',
        },
        {
          text: this.$t('admin_subscriber'),
          value: 'ownerEmail',
          sortable: false,
          width: '200',
        },
        {
          text: this.$t('common_startDate'),
          value: 'startDate',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('common_subscriptionsEnddate'),
          value: 'endDate',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('common_type'),
          value: 'type',
          sortable: false,
          width: '100',
        },
        {
          text: this.$t('common_state'),
          value: 'status',
          sortable: false,
          width: '50',
        },
        {
          text: this.$t('common_price'),
          value: 'grossValue',
          class: 'text-sm-end',
          sortable: false,
          width: '100',
        },
        {
          text: this.$t('admin_vat'),
          value: 'vatTax',
          class: 'text-sm-end',
          sortable: false,
          width: '80',
        },
        {
          text: this.$t('admin_whoAdded'),
          value: 'whoAddedEmail',
          sortable: false,
          width: '150',
        },
        {
          text: this.$t('common_comment'),
          value: 'comment',
          sortable: false,
        },
        {
          text: this.$t('actions.actions'),
          class: 'text-sm-end',
          sortable: false,
          width: '190',
        },
      ],
    };
  },
  computed: ({
    statusItems() {
      const statuses = this.possibleStatuses.map(
        (row) => ({
          text: row.name,
          value: row.name,
          disabled: false,
          // icon: this.filters.issuers.icons[row.name] ?? '',
        }),
      );

      return statuses;
    },
  }),
  watch: {
    statuses() {
      this.pagination.page = 1;
      this.getSubscriptionDetails();
    },
    filtering: {
      handler() {
        this.getSubscriptionDetails();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.sortDesc !== newValue.sortDesc
          || oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage
          || oldValue.sortBy !== newValue.sortBy) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
            this.getSubscriptionDetails(true);
            return;
          }
          this.getSubscriptionDetails();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getSubscriptionDetails();
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.getSubscriptionDetails(true);
    }, 1000);
  },
  methods: {
    exportAsyncParams(item) {
      return {
        id: item.id,
      };
    },
    getToggleTypesFiltering() {
      const possible = this.possibleStatuses.map(
        (row) => row.name,
      );

      const data = intersection(possible, this.statuses);

      return data.length ? data.join(',') : '';
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    downloadReport(subscriberId) {
      const url = `/administration/subscription/${subscriberId}/report`;
      const filename = `subscription-report-${subscriberId}.pdf`;
      this.onExport({
        url,
        filetype: 'application/pdf',
        filename,
      });
      this.axios.get(
        `/administration/subscription/report/${subscriberId}`,
      )
        .then((response) => {
          if (response.status === 200) {
            this.showSnackbar(
              'success',
              this.$t('common_success'),
            );
          }
        });
    },
    buttondDisabled(item) {
      return item.status === 'canceled'
        || item.status === 'manually_canceled'
        || !item.editable;
    },
    buttondConfirmDisabled(item) {
      return item.status !== 'initiated_proforma'
        || !item.editable;
    },
    getStatusColor(status) {
      switch (status) {
        case 'manually_canceled': return 'red';
        case 'canceled': return 'red';
        case 'error': return 'red';
        case 'paid': return 'green';
        case 'initiated': return 'orange';
        case 'initiated_proforma': return 'orange';
        default: return 'black';
      }
    },
    getStatusText(status) {
      return this.$t(`administration.subscription.status.${status}`);
    },
    cancelSubscription(subscriptionId) {
      this.loader = true;
      this.axios.delete(
        `/administration/subscription/${subscriptionId}`,
      )
        .then(() => {
          this.loader = false;
          this.getSubscriptionDetails();
        });
    },
    cancelSubscriptionAndCLoseDialog() {
      this.cancelSubscription(this.subscriptionToDeleteId);
      this.deleteDialog = false;
    },
    openDeleteDialog(subscriptionToDeleteId) {
      this.subscriptionToDeleteId = subscriptionToDeleteId;
      this.deleteDialog = true;
    },
    confirm(subscriptionId) {
      this.loader = true;
      this.axios.post(
        `/administration/subscription/${subscriptionId}/confirm`,
      )
        .then(() => {
          this.loader = false;
          this.getSubscriptionDetails();
        });
    },
    onDateRangeChange({
      from,
      to,
    }) {
      this.filtering.search.dateFrom = from;
      this.filtering.search.dateTo = to;
    },
    getSubscriptionDetails(resetPagination = false) {
      this.loader = true;
      if (resetPagination) {
        this.pagination.page = 1;
      }
      this.axios.get(
        '/administration/subscriptions',
        {
          params: {
            itemsPerPage: this.pagination.itemsPerPage, // add status
            pageNumber: this.pagination.page,
            status: this.getToggleTypesFiltering(),
            dateFrom: this.filtering.search.dateFrom
              ? `${this.filtering.search.dateFrom}`
              : null,
            dateTo: this.filtering.search.dateTo
              ? `${this.filtering.search.dateTo}`
              : null,
            search: this.search,
          },
        },
      )
        .then((response) => {
          this.subscriptionData = response.data.items;
          this.dataTable.totalItems = response.data.totalItems;
          this.possibleStatuses = response.data.statuses;
          this.loader = false;
        });
    },
  },
};
</script>
