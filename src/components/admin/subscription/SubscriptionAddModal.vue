<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      scrollable
      persisten
      content-class="dialogWidth-3"
      class="modal"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('admin_add') }}
            </h5>
          </span>
        </v-card-title>
        <v-progress-linear
          v-if="loaders.site"
          :indeterminate="true"
          class="mt-0"
        />
        <v-card-text class="pt-6">
          <div class="text-center">
            <v-progress-circular
              v-if="loaders.site"
              class="circleProgress"
              :size="90"
              :width="7"
              color="primary"
              indeterminate
            />
          </div>
          <template v-if="!loaders.site">
            <v-container grid-list-md>
              <v-expansion-panels>
                <v-expansion-panel>
                  <v-expansion-panel-header>
                    <h3>
                      {{ $t('admin_subscriber') }}
                    </h3>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <invoice-company-data-edit
                      key="invoice-subscriber-buyer"
                      :subscriber-id="parseInt(subscriberId, 10)"
                      :is-editable="false"
                      :show-notice="false"
                      :show-header="false"
                      :show-all-fields="false"
                      data-url="/administration/subscriber"
                    />
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
              <v-form
                ref="formUserEdit"
                v-model="form.valid"
                lazy-validation
              >
                <v-layout wrap>
                  <v-col
                    cols="12"
                    class="pt-0 pb-0"
                  >
                    <v-radio-group
                      v-model="payer"
                      :label="$t('admin_whoPays')"
                      row
                    >
                      <v-radio
                        :label="$t('common_client')"
                        value="client"
                      />
                      <v-radio
                        :label="$t('admin_subscriptionDealer')"
                        value="dealer"
                      />
                      <v-radio
                        label="i2m"
                        value="i2m"
                      />
                      <v-radio
                        label="bkf"
                        value="bkf"
                      />
                    </v-radio-group>
                  </v-col>
                  <v-col
                    cols="12"
                    class="pt-0 pb-0"
                  >
                    <v-radio-group
                      v-model="document"
                      :label="$t('common_document')"
                      row
                    >
                      <v-radio
                        :label="$t('admin_dontIssue')"
                        value="none"
                      />
                      <v-radio
                        :label="$t('admin_issue')"
                        value="issue"
                      />
                      <v-radio
                        :label="$t('admin_issueAndSend')"
                        value="send"
                      />
                    </v-radio-group>
                  </v-col>
                  <v-col
                    cols="6"
                    class="pt-0 pb-0"
                  >
                    <v-menu
                      v-model="dataPickerMenu"
                      :close-on-content-click="false"
                      :nudge-right="40"
                      transition="scale-transition"
                      offset-y
                      min-width="auto"
                    >
                      <template #activator="{ on, attrs }">
                        <v-text-field
                          v-model="subscription.startDate"
                          :label="$t('common_startDate')"
                          prepend-icon="mdi-calendar-today"
                          readonly
                          required
                          v-bind="attrs"
                          v-on="on"
                        />
                      </template>
                      <v-date-picker
                        v-model="subscription.startDate"
                        :locale="locale"
                        @input="dataPickerMenu = false"
                      />
                    </v-menu>
                  </v-col>

                  <v-col
                    cols="6"
                    class="pt-0 pb-0"
                  >
                    <v-menu
                      v-model="dataEndPickerMenu"
                      :close-on-content-click="false"
                      :nudge-right="40"
                      transition="scale-transition"
                      offset-y
                      min-width="auto"
                    >
                      <template #activator="{ on, attrs }">
                        <v-text-field
                          v-model="subscription.endDate"
                          :label="$t('common_subscriptionsEnddate')"
                          prepend-icon="mdi-calendar"
                          readonly
                          required
                          v-bind="attrs"
                          v-on="on"
                        />
                      </template>
                      <v-date-picker
                        v-model="subscription.endDate"
                        :locale="locale"
                        @input="dataEndPickerMenu = false"
                      />
                    </v-menu>
                  </v-col>
                  <v-col
                    sm="6"
                    class="pt-0 pb-0"
                  >
                    <v-autocomplete
                      v-model="subscription.currency"
                      v-validate="'required|min:1'"
                      item-value="id"
                      :label="$t('common_currency')"
                      :items="currencyOptions"
                      :autocomplete="true"
                      prepend-icon="mdi-currency-usd"
                      name="currency"
                      :data-vv-as="`${$t('admin_subscription')}`"
                      :error-messages="errors.collect('currency')"
                      :rules="rules.selectRequired"
                      required
                    />
                  </v-col>
                  <v-col
                    sm="6"
                    class="pt-0 pb-0"
                  >
                    <v-autocomplete
                      v-model="subscription.packageId"
                      v-validate="'required|min:1'"
                      item-value="id"
                      :label="$t('admin_subscription')"
                      :items="packagesOptions"
                      :autocomplete="true"
                      prepend-icon="mdi-format-list-numbered"
                      name="package"
                      :data-vv-as="`${$t('admin_subscription')}`"
                      :error-messages="errors.collect('package')"
                      :rules="rules.selectRequired"
                      required
                    />
                  </v-col>
                  <!--
                  <v-col
                    sm="12"
                    class="pt-0 pb-0"
                  >
                    <v-autocomplete
                      v-show="payer==='dealer'"
                      v-model="subscription.dealer"
                      v-validate="'required|min:1'"
                      item-value="id"
                      item-text="email"
                      :label="$t('admin_dealer')"
                      :items="dealersOptions"
                      :autocomplete="true"
                      prepend-icon="mdi-account"
                      name="dealer"
                      :data-vv-as="`${$t('admin_dealer')}`"
                      :error-messages="errors.collect('dealer')"
                      :rules="rules.selectRequiredDealer"
                      required
                    />
                  </v-col> -->
                  <v-col
                    sm="12"
                    class="pt-0 pb-0"
                  >
                    <v-textarea
                      v-model="subscription.comment"
                      prepend-icon="mdi-comment"
                      counter="250"
                      required
                      :rules="rules.selectRequired"
                      :label="$t('admin_comment')"
                    />
                  </v-col>
                </v-layout>
              </v-form>
              <template v-if="showTable">
                <table class="pt-2 summary-table">
                  <tr>
                    <td>
                      {{ $t('subscription.table.position') }}
                    </td>
                    <td>
                      {{ $t('subscription.table.type') }}
                    </td>
                    <td class="text-end">
                      {{ $t('common_document') }}
                    </td>
                    <td class="text-end">
                      {{ $t('subscription.table.price-before-discount') }}
                    </td>
                  </tr>
                  <tr
                    v-for="(it, index) in subscriptionCalculation.item"
                    :key="index"
                  >
                    <td>
                      {{ it.position }}
                    </td>
                    <td
                      :class="getTypeColor(it.type)"
                    >
                      {{ $t(`subscription.carwash-type.${it.type}`) }}
                    </td>
                    <td class="text-end">
                      {{ it.invoice ?? '-' }}
                    </td>
                    <td class="text-end">
                      {{ it.totalPrice|currencySymbol(currencySym) }}
                    </td>
                  </tr>
                  <tr class="font-weight-bold">
                    <td />
                    <td />
                    <td>
                      {{ $t('subscription.table.sum') }}
                    </td>
                    <td class="text-end">
                      {{
                        subscriptionCalculation.baseValue|currencySymbol(currencySym)
                      }}
                    </td>
                  </tr>
                </table>

                {{ $t('subscription.table.summary') }}
                <table class="pt-2 summary-table">
                  <tr class="font-weight-bold">
                    <td>
                      {{ $t('subscription.table.type') }}
                    </td>
                    <td>
                      {{ $t('subscription.table.price-before-discount') }}
                    </td>
                    <td>
                      {{ $t('subscription.table.discount') }}
                    </td>
                    <td class="text-end">
                      {{ $t('subscription.table.price-after-discount') }}
                    </td>
                  </tr>
                  <tr
                    v-for="(summary, index) in subscriptionCalculation.summary"
                    :key="index"
                  >
                    <td>
                      {{ $t(`subscription.table.${summary.type}`) }}
                    </td>
                    <td class="text-end">
                      {{ summary.baseValue|currencySymbol(currencySym) }}
                    </td>
                    <td class="text-end">
                      {{ summary.discount }}%
                    </td>
                    <td class="font-weight-bold text-end">
                      {{ summary.valueAfterDiscount|currencySymbol(currencySym) }}
                    </td>
                  </tr>
                </table>
              </template>
            </v-container>
          </template>
        </v-card-text>
        <v-card-actions v-if="!loaders.site">
          <v-spacer />
          <v-btn
            color="gray"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            color="secondary"
            :loading="loaders.actualize"
            :disabled="!form.valid"
            @click.native="save"
          >
            {{ $t('admin_save') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loaders.actualize"
            :disabled="!form.valid"
            @click.native="submit"
          >
            {{ $t('admin_add') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>

import invoiceCompanyDataEdit from '@components/user/InvoiceCompanyDataEdit.vue';

export default {
  components: {
    invoiceCompanyDataEdit,
  },
  props: {
    onSuccess: {
      type: Function,
      default: () => {},
    },
    discountPercent: {
      type: Number,
      default: 0,
    },
    subscriberId: {
      type: Number,
      required: false,
      default: null,
    },
    dealerId: {
      type: Number,
      required: false,
      default: null,
    },
  },
  data() {
    return {
      discount: this.discountPercent,
      locale: 'pl',
      dataPickerMenu: false,
      dataEndPickerMenu: false,
      loaders: {
        site: false,
        actualize: false,
      },
      currencyCode: 'PLN',
      currencySym: 'zł',
      subscriptionCalculation: {
        items: {},
        summary: {
          WARRANTY: 0,
          STANDARD: 0,
        },
        summaryAfterDiscount: {
          WARRANTY: 0,
          STANDARD: 0,
        },
      },
      payer: 'client',
      document: 'issue',
      subscription: {
        // dealer: null,
        packageId: 0,
        startDate: '',
        endDate: '',
        comment: '',
        currency: '',
      },
      dialog: false,
      currencyOptions: [
        {
          id: 'PLN',
          text: 'PLN',
          symbol: 'zł',
        },
        {
          id: 'EUR',
          text: 'EUR',
          symbol: '€',
        },
      ],
      // dealersOptions: null,
      packagesOptions: [
        {
          id: 'basic',
          code: 'basic',
          text: 'basic',
        },
        {
          id: 'premium',
          code: 'premium',
          text: 'premium',
        },
      ],
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      rules: {
        // selectRequiredDealer: [(v) => (!!v || this.payer !== 'dealer')
        // || this.$t('common_fieldRequired')],
        selectRequired: [(v) => !!v || this.$t('common_fieldRequired')],
        selectRequired2: [(v) => v.length > 0 || this.$t('common_fieldRequired')],
      },
    };
  },
  computed: {
    showTable() {
      if (
        this.payer !== ''
        && this.subscription.endDate !== ''
        && this.subscription.startDate !== ''
        && this.subscription.packageId !== 0
        && this.subscription.currency !== ''
      ) {
        this.getPriceCalculation();
        return true;
      }

      return false;
    },
  },
  watch: {
    dialog(val) {
      // get user data only when dialog shows up
      if (val) {
        // this.getDealers();
        this.loaders.actualize = false;
        this.resetValidationErrorsAndClearFields();
      }
    },
  },
  methods: {
    validateAll() {
      this.$refs.formUserEdit.validate();
    },
    resetValidationErrorsAndClearFields() {
      this.loaders.site = false;
      this.loaders.actualize = false;
      if (this.$refs.formUserEdit) {
        this.$refs.formUserEdit.reset();
        this.$refs.formUserEdit.resetValidation();
        this.$validator.reset();
      }
      this.clearFormData();
    },
    // getDealers() {
    //   this.axios.get(
    //     `/api/user/${this.$route.params.id}/dealer`,
    //   )
    //     .then((response) => {
    //       if ((response.status === 200) && response.data) {
    //         this.dealersOptions = response.data;
    //       }
    //     });
    // },
    onError() {
      // on error
      this.closeDialog();
    },
    getPriceCalculation() {
      this.axios.post(
        `/administration/subscriber/${this.$route.params.id}/calculate`,
        {
          from: this.subscription.startDate,
          to: this.subscription.endDate,
          currency: this.subscription.currency,
          package: this.subscription.packageId,
          payer: this.payer,
          document: this.document,
        },
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.subscriptionCalculation = response.data;
              this.currencyCode = response.data.currencyCode;
              this.currencySym = response.data.currencySymbol;
              // this.onSuccess();
              // this.closeDialog();
            }
          },
          () => {
            // on error
            this.loaders.site = false;
            this.loaders.actualize = false;
          },
        );
    },
    getTypeColor(type) {
      if (type === 'WARRANTY') {
        return 'green--text';
      }

      if (type === 'UNSUBSCRIBED') {
        return 'orange--text';
      }

      return '';
    },
    requestParameters() {
      return {
        from: this.subscription.startDate,
        to: this.subscription.endDate,
        package: this.subscription.packageId,
        payer: this.payer,
        document: this.document,
        // dealer: this.subscription.dealer,
        comment: this.subscription.comment,
        currency: this.subscription.currency,
      };
    },
    save() {
      this.$validator.validateAll();
      if (this.$refs.formUserEdit.validate()) {
        this.loaders.site = true;
        this.loaders.actualize = true;

        this.axios.post(
          `/administration/subscriber/${this.$route.params.id}/subscriptions`,
          {
            ...this.requestParameters(),
            status: 'initiated_proforma',
          },
        )
          .then(
            (response) => {
              if (response.status === 200) {
                this.onSuccess();
                this.closeDialog();
              }
            },
            () => {
              // on error
              this.loaders.site = false;
              this.loaders.actualize = false;
            },
          );
      }
    },
    submit() {
      this.$validator.validateAll();
      if (this.$refs.formUserEdit.validate()) {
        this.loaders.site = true;
        this.loaders.actualize = true;

        this.axios.post(
          `/administration/subscriber/${this.$route.params.id}/subscriptions`,
          this.requestParameters(),
        )
          .then(
            (response) => {
              if (response.status === 200) {
                this.onSuccess();
                this.closeDialog();
              }
            },
            () => {
              // on error
              this.loaders.site = false;
              this.loaders.actualize = false;
            },
          );
      }
    },
    clearFormData() {
      this.payer = 'client';
      this.subscription = {
        // dealer: null,
        packageId: 0,
        startDate: '',
        endDate: '',
        comment: '',
        currency: '',
      };

      this.subscriptionCalculation = {
        item: {},
        summary: {
          WARRANTY: 0,
          STANDARD: 0,
        },
        summaryAfterDiscount: {
          WARRANTY: 0,
          STANDARD: 0,
        },
      };
    },
    closeDialog() {
      this.resetValidationErrorsAndClearFields();
      this.dialog = false;
    },
  },
};
</script>

<style scoped>
.summary-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px black solid;
}

.summary-table td {
  padding: 5px;
  border: 1px black solid;
}

.card-text-wrap {
  display: flex;
  flex-direction: column;
}

.card-text-wrap p {
  font-size: 18px;
  text-align: center;
}

</style>
