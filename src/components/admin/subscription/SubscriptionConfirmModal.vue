<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      scrollable
      persisten
      content-class="dialogWidth-3"
      class="modal"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('admin_confirm') }}
            </h5>
          </span>
        </v-card-title>
        <v-progress-linear
          v-if="loaders"
          :indeterminate="true"
          class="mt-0"
        />
        <v-card-text class="pt-6">
          <div class="text-center">
            <v-progress-circular
              v-if="loaders"
              class="circleProgress"
              :size="90"
              :width="7"
              color="primary"
              indeterminate
            />
          </div>
          <template v-if="!loaders">
            <v-container grid-list-md>
              <v-form
                ref="subscriptionPreview"
                v-model="form.valid"
                lazy-validation
              >
                <v-layout wrap>
                  <v-col
                    cols="12"
                    class="pt-0 pb-0"
                  >
                    <v-radio-group
                      v-model="document"
                      :label="$t('common_document')"
                      row
                    >
                      <v-radio
                        :label="$t('admin_dontIssue')"
                        value="none"
                      />
                      <v-radio
                        :label="$t('admin_issue')"
                        value="issue"
                      />
                      <v-radio
                        :label="$t('admin_issueAndSend')"
                        value="send"
                      />
                    </v-radio-group>
                  </v-col>
                  <v-col
                    cols="6"
                    class="pt-0 pb-0"
                  >
                    <v-menu
                      v-model="dataPickerMenu"
                      :close-on-content-click="false"
                      :nudge-right="40"
                      transition="scale-transition"
                      offset-y
                      min-width="auto"
                    >
                      <template #activator="{ on, attrs }">
                        <v-text-field
                          v-model="subscription.startDate"
                          :label="$t('common_startDate')"
                          prepend-icon="mdi-calendar-today"
                          readonly
                          required
                          v-bind="attrs"
                          v-on="on"
                        />
                      </template>
                      <v-date-picker
                        v-model="subscription.startDate"
                        :locale="locale"
                        @input="calculateDates()"
                      />
                    </v-menu>
                  </v-col>

                  <v-col
                    cols="6"
                    class="pt-0 pb-0"
                  >
                    <v-menu
                      v-model="dataEndPickerMenu"
                      :close-on-content-click="false"
                      :nudge-right="40"
                      transition="scale-transition"
                      offset-y
                      min-width="auto"
                    >
                      <template #activator="{ on, attrs }">
                        <v-text-field
                          v-model="subscription.endDate"
                          :disabled="true"
                          :label="$t('common_subscriptionsEnddate')"
                          prepend-icon="mdi-calendar"
                          readonly
                          required
                          v-bind="attrs"
                          v-on="on"
                        />
                      </template>
                      <v-date-picker
                        v-model="subscription.endDate"
                        :locale="locale"
                        @input="dataEndPickerMenu = false"
                      />
                    </v-menu>
                  </v-col>
                  <v-col
                    sm="6"
                    class="pt-0 pb-0"
                  >
                    <v-autocomplete
                      v-model="subscription.currency"
                      v-validate="'required|min:1'"
                      item-value="id"
                      :label="$t('common_currency')"
                      :items="currencyOptions"
                      :autocomplete="true"
                      prepend-icon="mdi-currency-usd"
                      name="currency"
                      :data-vv-as="`${$t('admin_subscription')}`"
                      :error-messages="errors.collect('currency')"
                      :rules="rules.selectRequired"
                      :disabled="true"
                      required
                    />
                  </v-col>
                  <v-col
                    sm="6"
                    class="pt-0 pb-0"
                  >
                    <v-autocomplete
                      v-model="subscription.packageId"
                      v-validate="'required|min:1'"
                      item-value="id"
                      :label="$t('admin_subscription')"
                      :items="packagesOptions"
                      :autocomplete="true"
                      prepend-icon="mdi-format-list-numbered"
                      name="package"
                      :data-vv-as="`${$t('admin_subscription')}`"
                      :error-messages="errors.collect('package')"
                      :rules="rules.selectRequired"
                      :disabled="true"
                      required
                    />
                  </v-col>
                  <v-col
                    sm="12"
                    class="pt-0 pb-0"
                  >
                    <v-textarea
                      v-model="subscription.comment"
                      prepend-icon="mdi-comment"
                      counter="250"
                      required
                      :rules="rules.selectRequired"
                      :label="$t('admin_comment')"
                    />
                  </v-col>
                </v-layout>
              </v-form>
              <table class="pt-2 summary-table">
                <tr>
                  <td>
                    {{ $t('subscription.table.position') }}
                  </td>
                  <td>
                    {{ $t('subscription.table.type') }}
                  </td>
                  <td class="text-end">
                    {{ $t('common_document') }}
                  </td>
                  <td class="text-end">
                    {{ $t('subscription.table.price-before-discount') }}
                  </td>
                </tr>
                <tr
                  v-for="(it, index) in subscriptionCalculation.item"
                  :key="index"
                >
                  <td>
                    {{ it.position }}
                  </td>
                  <td
                    :class="getTypeColor(it.type)"
                  >
                    {{ $t(`subscription.carwash-type.${it.type}`) }}
                  </td>
                  <td class="text-end">
                    {{ it.invoice ?? '-' }}
                  </td>
                  <td class="text-end">
                    {{ it.totalPrice|currencySymbol(currencySym) }}
                  </td>
                </tr>
                <tr class="font-weight-bold">
                  <td />
                  <td />
                  <td>
                    {{ $t('subscription.table.sum') }}
                  </td>
                  <td class="text-end">
                    {{
                      subscriptionCalculation.baseValue|currencySymbol(currencySym)
                    }}
                  </td>
                </tr>
              </table>

              {{ $t('subscription.table.summary') }}
              <table class="pt-2 summary-table">
                <tr class="font-weight-bold">
                  <td>
                    {{ $t('subscription.table.type') }}
                  </td>
                  <td>
                    {{ $t('subscription.table.price-before-discount') }}
                  </td>
                  <td>
                    {{ $t('subscription.table.discount') }}
                  </td>
                  <td class="text-end">
                    {{ $t('subscription.table.price-after-discount') }}
                  </td>
                </tr>
                <tr
                  v-for="(summary, index) in subscriptionCalculation.summary"
                  :key="index"
                >
                  <td>
                    {{ $t(`subscription.table.${summary.type}`) }}
                  </td>
                  <td class="text-end">
                    {{ summary.baseValue|currencySymbol(currencySym) }}
                  </td>
                  <td class="text-end">
                    {{ summary.discount }}%
                  </td>
                  <td class="font-weight-bold text-end">
                    {{ summary.valueAfterDiscount|currencySymbol(currencySym) }}
                  </td>
                </tr>
              </table>
            </v-container>
          </template>
        </v-card-text>
        <v-card-actions v-if="!loaders">
          <v-spacer />
          <v-btn
            color="gray"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loaders.actualize"
            :disabled="!form.valid"
            @click.native="confirm()"
          >
            {{ $t('admin_confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>

import {
  addDays,
  differenceInDays,
} from 'date-fns';

export default {
  props: {
    onSuccess: {
      type: Function,
      default: () => {},
    },
    subscriptionId: {
      type: Number,
      default: 57646,
    },
  },
  data() {
    return {
      discount: 0,
      locale: 'pl',
      dataPickerMenu: false,
      dataEndPickerMenu: false,
      dateDifference: 0,
      loaders: {
        site: false,
        actualize: false,
      },
      currencyCode: 'PLN',
      currencySym: 'zł',
      subscriptionCalculation: {
        item: {},
        summary: {
          WARRANTY: 0,
          STANDARD: 0,
        },
        summaryAfterDiscount: {
          WARRANTY: 0,
          STANDARD: 0,
        },
      },
      document: 'issue',
      subscription: {
        packageId: 0,
        startDate: '',
        endDate: '',
        comment: '',
        currency: '',
      },
      dialog: false,
      currencyOptions: [
        {
          id: 'PLN',
          text: 'PLN',
          symbol: 'zł',
        },
        {
          id: 'EUR',
          text: 'EUR',
          symbol: '€',
        },
      ],
      // dealersOptions: null,
      packagesOptions: [
        {
          id: 'basic',
          code: 'basic',
          text: 'basic',
        },
        {
          id: 'premium',
          code: 'premium',
          text: 'premium',
        },
      ],
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      rules: {
        selectRequired: [(v) => !!v || this.$t('common_fieldRequired')],
        selectRequired2: [(v) => v.length > 0 || this.$t('common_fieldRequired')],
      },
    };
  },
  watch: {
    dialog(val) {
      // get user data only when dialog shows up
      if (val) {
        this.loaders = true;
        this.resetValidationErrorsAndClearFields();
        this.getData();
      }
    },
  },
  methods: {
    validateAll() {
      this.$refs.subscriptionPreview.validate();
    },
    resetValidationErrorsAndClearFields() {
      if (this.$refs.subscriptionPreview) {
        this.$refs.subscriptionPreview.reset();
        this.$refs.subscriptionPreview.resetValidation();
        this.$validator.reset();
      }
      this.clearFormData();
    },
    onError() {
      this.closeDialog();
    },
    getData() {
      this.loaders = true;
      this.axios.get(
        `/administration/subscription/${this.subscriptionId}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.discount = response.data.discount;
              this.subscription.startDate = this.$options
                .filters.formatDateDay(response.data.startDate);
              this.subscription.endDate = this.$options
                .filters.formatDateDay(response.data.endDate);
              this.subscriptionCalculation = response.data;
              this.subscription.comment = response.data.comment;
              this.subscription.currency = response.data.currencyCode;
              this.currencyCode = response.data.currencyCode;
              this.currencySym = response.data.currencySymbol;
              this.subscription.packageId = response.data.type;
              this.loaders = false;
              this.dateDifference = differenceInDays(
                response.data.endDate,
                response.data.startDate,
              );
            }
          },
          () => {
            // on error
            this.loaders = false;
          },
        );
    },
    getTypeColor(type) {
      if (type === 'WARRANTY') {
        return 'green--text';
      }

      if (type === 'UNSUBSCRIBED') {
        return 'orange--text';
      }

      return '';
    },
    calculateDates() {
      this.dataPickerMenu = false;

      this.subscription.endDate = this.$options.filters.formatDateDay(
        addDays(this.subscription.startDate, this.dateDifference),
      );
    },
    clearFormData() {
      this.subscription = {
        // dealer: null,
        packageId: 0,
        startDate: '',
        endDate: '',
        comment: '',
        currency: '',
      };

      this.subscriptionCalculation = {
        item: {},
        summary: {
          WARRANTY: 0,
          UNSUBSCRIBED: 0,
          STANDARD: 0,
        },
        summaryAfterDiscount: {
          WARRANTY: 0,
          UNSUBSCRIBED: 0,
          STANDARD: 0,
        },
      };
    },
    closeDialog() {
      this.resetValidationErrorsAndClearFields();
      this.dialog = false;
    },
    confirm() {
      this.loader = true;
      this.axios.post(
        `/administration/subscription/${this.subscriptionId}/confirm`,
        {
          startDate: this.subscription.startDate,
          comment: this.subscription.comment,
          document: this.document,
        },
      )
        .then(() => {
          this.loader = false;
          this.onSuccess();
          this.closeDialog();
        });
    },
  },
};
</script>

<style scoped>
.summary-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px black solid;
}

.summary-table td {
  padding: 5px;
  border: 1px black solid;
}

.card-text-wrap {
  display: flex;
  flex-direction: column;
}

.card-text-wrap p {
  font-size: 18px;
  text-align: center;
}

</style>
