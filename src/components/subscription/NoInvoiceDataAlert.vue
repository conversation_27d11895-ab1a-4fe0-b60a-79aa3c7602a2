<template>
  <v-col
    v-if="isOwner && !isDataCompleted"
    cols="12"
  >
    <v-alert
      class="no-margin justify-space-between"
      :value="true"
      type="info"
      border="left"
    >
      <v-card
        class="d-flex justify-space-between"
        flat
        tile
        color="info"
        elevation="0"
      >
        <v-card-text
          class="px-2 py-2"
          color="info"
          elevation="0"
        >
          <h2>{{ $t('subscription_alertContent') }}</h2>
        </v-card-text>
        <v-card
          elevation="0"
          color="info"
          flat
          tile
        >
          <v-btn
            class="go-to"
            :href="formUrl"
            color="primary"
          >
            {{ $t('subscription_goToDataCompletion') }}
          </v-btn>
        </v-card>
      </v-card>
    </v-alert>
  </v-col>
</template>

<script>

import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      isDataCompleted: true,
    };
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
    }),
  },
  created() {
    // this.checkInvoiceData();
  },
  methods: {
    checkInvoiceData() {
      this.axios.get('')
        .then((response) => {
          if (response.data) {
            this.isDataCompleted = response.data.isDataCompleted;
          }
        })
        .catch(() => {
        });
    },
  },
};
</script>
