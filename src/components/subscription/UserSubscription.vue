<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-credit-card</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('subscription_yourSubscription') }}
      </v-toolbar-title>
      <v-spacer />
    </v-toolbar>
    <v-card-text>
      <v-layout
        row
        wrap
      >
        <v-col cols="12">
          <v-card-text
            dark
            class="subscription-card"
          >
            <div
              v-if="loader"
              class="loader-background"
            >
              <v-progress-circular
                size="50"
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
            <template v-if="!loader">
              <h1 class="text-sm-start">
                {{ $t('subscriptions.subscription') }} {{ $t(`subscriptions.types.${type}`) }}
              </h1>

              <div class="user-subscription-btns pt-4 pb-4">
                <v-btn
                  :disabled="disabled"
                  color="primary"
                  @click.native="openModal('subscriptionPayDialog')"
                >
                  <v-icon class="mr-2">
                    mdi-credit-card
                  </v-icon>
                  {{ $t('subscriptions.actions.chose-and-pay') }}
                </v-btn>
              </div>
            </template>
          </v-card-text>
        </v-col>
      </v-layout>
    </v-card-text>
    <subscription-payment-modal ref="subscriptionPayDialog" />
  </v-card>
</template>

<script>

import subscriptionPaymentModal from '@components/subscription/SubscriptionPaymentModal.vue';
import { mapGetters } from 'vuex';

export default {
  components: {
    subscriptionPaymentModal,
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loader: true,
      activeTo: '-',
      toPayValue: '-',
      type: 'free',
      ownerSubscriptionCurrnecy: 'PLN',
    };
  },
  computed: {
    ...mapGetters({
      currencySymbol: 'auth/userCurrencySymbol',
    }),
  },
  mounted() {
    this.getData();

    if (this.$route.params.paymentId !== undefined) {
      this.$refs.subscriptionPayDialog.dialog = true;
    }
  },
  methods: {
    getData() {
      this.loader = true;
      this.axios.get('/cm/subscription/manager/subscription_data')
        .then((response) => {
          if (response.data) {
            this.activeTo = response.data.activeToDate;
            this.toPayValue = response.data.toPayValue;
            this.type = response.data.type;
            this.loader = false;
          }
          this.loader = false;
        })
        .catch(() => {
          this.loader = false;
        });
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
  },
};
</script>

<style lang="stylus">
.user-subscription-btns
  float left

  button
    margin-left 0px

.subscription-card
  h1
    font-weight 500

  h4
    font-weight 500

.subscriptions
  min-height calc(100vh - 150px)
</style>
