<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Informacje od serwisanta
          </h5>
        </span>
        <v-spacer />
      </v-card-title>
      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-row>
          <v-col>
            <details-table
              class="my-4"
              :data="visitSummaryMain"
            >
              <template #Zalecana-ponowna-wizyta?="{ value }">
                <v-layout justify-start>
                  <bool-translate :bool-value="value" />
                </v-layout>
              </template>
            </details-table>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            Dane ze stanowisk:
            <v-simple-table
              dense
              class="elevation-3 mx-1 mt-2 mb-3"
            >
              <template #default>
                <thead>
                  <tr>
                    <th class="text-left">
                      <PERSON><PERSON><PERSON>o
                    </th>
                    <th class="text-left">
                      Numer
                    </th>
                    <th class="text-left">
                      Stand licznika
                    </th>
                    <th class="text-left">
                      Status Kasy fiskalnej
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="stand in data.stands"
                    :key="stand.id"
                  >
                    <td>
                      <v-layout justify-start>
                        <stand-kind :kind="stand.kind" />
                      </v-layout>
                    </td>
                    <td>{{ stand.bayId ?? '-' }}</td>
                    <td>{{ stand.counter ?? '-' }}</td>
                    <td>{{ stand.fiscalStatus?.textPL ?? '-' }}</td>
                  </tr>
                </tbody>
              </template>
            </v-simple-table>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import DetailsTable from '@views/app/service/dashboard/DetailsTable.vue';
import StandKind from '@components/domain/service/StandKind.vue';
import BoolTranslate from '@components/common/BoolTranslate.vue';

export default {
  name: 'VisitSummary',
  components: {
    StandKind,
    DetailsTable,
    BoolTranslate,
  },
  mixins: [
    SnackbarMixin,
  ],
  props: {
    id: {
      type: Number,
      default: null,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  computed: {
    visitSummaryMain() {
      const summary = {
        'Zastana ilość stanowisk wyłączonych z pracy': this.data.inoperableStandCount ?? '-',
        'Używana chemia': this.data.chemical?.textPL ?? '-',
        'Ilość przejechanych kilometrów': this.data.distance ?? '-',
        'Czas rozpoczęcia': this.$options.filters.formatDateDayTime(this.data.visitAttendedTime),
        'Czas zakoczeńczenia': this.$options.filters.formatDateDayTime(this.data.visitDepartedTime),
        'Ilość roboczogodzin': this.data.workHours ?? '-',
        'Status eksploatacyjny urządzenia': this.data.deviceOperableStatus?.textPL ?? '-',
        'Zalecenia dla właściciela': this.data.recommendationsToClient ?? '-',
        'Zalecana ponowna wizyta?': this.data.isAdditionalVisitRequired,
      };

      if (this.data.isAdditionalVisitRequired) {
        summary['Opis problemu/nieprawidłowości'] = this.data.additionalVisitRemarks ?? '-';
      }

      return summary;
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/api/service/visit/${this.id}/summary`);
        this.data = response.data;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
  },
};
</script>
