<template>
  <div>
    <btn-edit
      @click="openDialog()"
    />
    <v-dialog
      v-model="dialog"
      scrollable
      persisten
      content-class="dialogWidth-3"
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('common_cyclicTopUpsHeading') }}
            </h5>
          </span>
        </v-card-title>
        <v-progress-linear
          v-if="loaders.site"
          :indeterminate="true"
          class="mt-0"
        />
        <v-card-text class="pt-6">
          <div class="text-center">
            <v-progress-circular
              v-if="loaders.site"
              class="circleProgress"
              :size="90"
              :width="7"
              color="primary"
              indeterminate
            />
          </div>
          <template v-if="!loaders.site">
            <template
              v-if="errorText !== null"
            >
              <v-alert
                border="left"
                class="mt-5"
                text
                type="error"
              >
                {{ errorText }}
              </v-alert>
            </template>
            <v-container
              grid-list-md
              class="pt-0"
            >
              <div>
                <v-radio-group
                  v-model="config.period"
                  :label="$t('common_frequency')"
                  row
                >
                  <v-radio
                    :label="$t('common_frequency_daily')"
                    value="daily"
                  />
                  <v-radio
                    :label="$t('common_frequency_weekly')"
                    value="weekly"
                  />
                  <v-radio
                    :label="$t('common_frequency_monthly')"
                    value="monthly"
                  />
                </v-radio-group>
                <v-text-field
                  v-model="config.title"
                  :label="$t('common_userTitle')"
                  autofocus
                  prepend-icon="mdi-mail"
                  required
                />
                <v-combobox
                  v-model="config.email"
                  :items="config.email"
                  prepend-icon="mdi-email-multiple"
                  hide-selected
                  :label="$t('common_email')"
                  :data-vv-as="$t('common_email')"
                  :error-messages="errors.collect('tableEmail')"
                  :rules="rules.comboboxEmail"
                  multiple
                  small-chips
                  deletable-chips
                >
                  <template #no-data>
                    <v-list-item>
                      <v-list-item-content>
                        <v-list-item-title>
                          {{ $t('common_pressEnterToAddNew') }}
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                </v-combobox>
                <v-radio-group
                  v-model="config.ext"
                  :label="$t('actions.chose_report_to_generate')"
                  row
                >
                  <v-radio
                    :label="$t('actions.export_xlsx')"
                    value="xlsx"
                  />
                  <v-radio
                    :label="$t('actions.export_pdf')"
                    value="pdf"
                  />
                  <v-radio
                    :label="$t('actions.export_csv')"
                    value="csv"
                  />
                </v-radio-group>
              </div>
            </v-container>
          </template>
        </v-card-text>
        <v-card-actions v-if="!loaders.site">
          <v-spacer />
          <v-btn
            color="gray"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loaders.actualize"
            @click.native="submit"
          >
            {{ $t('actions.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import BtnEdit from '@components/common/button/BtnEdit.vue';

export default {
  components: {
    BtnEdit,
  },
  mixins: [
    SnackbarMixin,
  ],
  props: {
    configId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      errorText: null,
      reportEmail: {
        model: [],
        items: [],
        search: null,
      },
      loaders: {
        site: true,
        actualize: false,
      },
      dialog: false,
      config: null,
      rules: {
        required: [(v) => !!v || this.$t('common_fieldRequired')],
      },
    };
  },
  watch: {
    dialog(val) {
      // get client data only when dialog shows up
      if (val) {
        this.loaders.actualize = false;
        this.resetValidationErrorsAndClearFields();
        this.getData();
      }
    },
  },
  methods: {
    resetValidationErrorsAndClearFields() {
      this.clearFormData();
    },
    getData() {
      this.loaders.site = true;
      this.loaders.actualize = false;
      this.axios.get(
        `/api/report_config/${this.configId}`,
      )
        .then(
          (response) => {
            this.config = {
              ...response.data,
            };

            this.loaders.site = false;
          },
          () => {
            this.onError();
          },
        );
    },
    onError() {
      // on error
      this.closeDialog();
    },
    editConfig() {
      this.loaders.site = true;
      this.loaders.actualize = true;

      const parameters = {
        email: this.config.email,
        ext: this.config.ext,
        period: this.config.period,
        title: this.config.title,
      };

      const url = `/api/report_config/${this.configId}`;

      this.axios.patch(
        url,
        parameters,
      )
        .then(
          () => {
            this.$emit('cyclic-config-edited', {});
            this.showSnackbar(
              'success',
              this.$t('common_success'),
            );
            this.closeDialog();
          },
          () => {
            this.showSnackbar(
              'error',
              this.$t('common_errorHeader'),
            );
            // on error
            this.loaders.site = false;
            this.loaders.actualize = false;
          },
        );
    },
    submit() {
      this.editConfig();
    },
    clearFormData() {
      this.config = {
        id: null,
        typs: null,
      };
    },
    closeDialog() {
      this.resetValidationErrorsAndClearFields();
      this.dialog = false;
    },
    openDialog() {
      this.dialog = true;
    },
    onDateRangeChange(dates) {
      this.config.startTime = dates.from;
      this.config.endTime = dates.to;
    },
  },
};
</script>

<style scoped>
.card-text-wrap {
  display: flex;
  flex-direction: column;
}

.card-text-wrap p {
  font-size: 18px;
  text-align: center;
}
</style>
