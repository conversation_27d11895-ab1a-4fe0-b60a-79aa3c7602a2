<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        sm="8"
        class="text-sm-start"
      >
        <h2>
          <span>{{ $t('finance_listHeading') }}</span>
        </h2>
      </v-col>
      <v-col
        cols="12"
        sm="4"
        class="d-flex justify-end"
      >
        <btn-refresh
          class="mr-2"
          @click="getData"
        />
      </v-col>
      <v-col
        cols="12"
      >
        <v-data-table
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :options.sync="pagination"
          :loading="loaders.site"
          :server-items-length="dataTable.totalItems"
          :footer-props="dataTable.footerProps"
        >
          <template #[`item.ctime`]="{ item }">
            {{ item.ctime|formatDateDayTime }}
          </template>
          <template #[`item.period`]="{ item }">
            {{ $t(`common_frequency_${item.period}`) }}
          </template>
          <template #[`item.actions`]="{ item }">
            <v-layout
              class="d-flex justify-end"
            >
              <cyclic-report-edit-modal
                :config-id="item.id"
                @cyclic-config-edited="getData()"
              />
              <report-config-generate-modal
                :id="item.id"
                @cyclic-report-generate="propageteReportGenerate()"
              />
              <delete-modal
                :text="$t('cyclicReport_deleteQuestion')"
                :url="`/api/report_config/${item.id}`"
                @success="getData"
              />
            </v-layout>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <v-row justify="center" />
  </v-container>
</template>

<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import CyclicReportEditModal from '@components/reports/config/ReportConfigEditModal.vue';
import ReportConfigGenerateModal from '@components/reports/config/ReportConfigGenerateModal.vue';
import snackbarMixin from '@components/mixins/SnackbarMixin.vue';
import DeleteModal from '@components/common/modal/DeleteModal.vue';

export default {
  components: {
    DeleteModal,
    BtnRefresh,
    CyclicReportEditModal,
    ReportConfigGenerateModal,
  },
  mixins: [
    snackbarMixin,
  ],
  data() {
    return {
      configToDeleteId: null,
      deleteDialog: false,
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['ctime'],
        sortDesc: [true],
      },
      dataTable: {
        totalItems: 0,
        items: [],
        footerProps: {
          'items-per-page-options': [10, 25, 50],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('finance_createTime'),
            value: 'ctime',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('finance_reportName'),
            value: 'title',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_email'),
            value: 'email',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('finance_extension'),
            value: 'ext',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_frequency'),
            value: 'period',
            showInRowExpand: true,
            sortable: false,
          },
          {
            value: 'actions',
            text: this.$t('actions.actions'),
            showInRowExpand: true,
            sortable: false,
            align: 'end',
          },
        ],
      },
      count: 0,
      loaders: {
        site: false,
      },
    };
  },
  watch: {
    filtering: {
      handler() {
        this.pagination.page = 1;
        this.getData();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.itemsPerPage !== newValue.itemsPerPage) {
          this.pagination.page = 1;
        }
        this.getData();
      },
      deep: true,
    },
  },
  mounted() {
    this.getData();
  },
  methods: {

    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    propageteReportGenerate() {
      this.$emit('cyclic-report-generate');
    },
    getData() {
      this.loaders.site = true;
      this.axios.get(
        '/api/report_configs',
        {
          params: {
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
          },
        },
      )
        .then(
          (response) => {
            this.dataTable.items = response.data.data;
            this.dataTable.totalItems = response.data.total;

            this.loaders.site = false;
          },
        );
    },
  },
};
</script>
