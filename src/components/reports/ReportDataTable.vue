<template>
  <div>
    <v-col cols="12" />
    <v-col cols="12">
      <v-layout
        row
        wrap
      >
        <v-col
          cols="12"
          sm="8"
          class="text-sm-start"
        >
          <h2 v-if="title">
            <span>
              {{ title }}
            </span>
          </h2>
        </v-col>
        <v-col
          cols="12"
          sm="4"
          class="d-flex justify-end"
        >
          <btn-refresh
            class="mr-2"
            @click="fetchData"
          />
        </v-col>
      </v-layout>
    </v-col>
    <v-col
      cols="12"
      class="pt-0"
    >
      <v-data-table
        dense
        mobile-breakpoint="0"
        :headers="computedHeaders"
        :items="items"
        :loading="loading"
        :options.sync="options"
        :server-items-length="totalItems"
        :items-per-page="options.itemsPerPage"
        :hide-default-footer="loading"
        :footer-props="footerProps"
      >
        <template #progress>
          <div class="text-center mx-n4">
            <v-progress-linear
              class="loader"
              indeterminate
              color="primary"
            />
          </div>
        </template>
        <template #item="slotProps">
          <tr>
            <td
              v-for="header in computedHeaders"
              :key="header.value"
              :class="getAlignClass(header.align)"
            >
              <slot
                :name="`item.${header.value}`"
                v-bind="slotProps"
              >
                {{ slotProps.item[header.value] }}
              </slot>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-col>
  </div>
</template>

<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  name: 'ReportDataTable',
  components: { BtnRefresh },
  mixins: [
    SnackbarMixin,
  ],
  props: {
    title: {
      type: String,
      required: false,
      default: null,
    },
    url: {
      type: String,
      required: true,
    },
    headers: {
      type: Array,
      default: () => [], // jeśli puste, generujemy automatycznie
    },
    filters: {
      type: Object,
      default: () => {}, // jeśli puste, generujemy automatycznie
    },
  },
  data() {
    return {
      items: [],
      totalItems: 0,
      loading: false,
      autoHeaders: [],
      footerProps: {
        'items-per-page-options': [5, 10, 20, 40],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      options: {
        page: 1,
        itemsPerPage: 10,
        sortBy: [],
        sortDesc: [],
      },
    };
  },
  computed: {
    computedHeaders() {
      return this.headers.length > 0 ? this.headers : this.autoHeaders;
    },
    params() {
      return {
        page: this.options.page,
        perPage: this.options.itemsPerPage,
        ...this.filters,
      };
    },
  },
  watch: {
    filters: {
      handler() {
        this.options.page = 1;
        this.fetchData();
      },
      deep: true,
    },
    options: {
      handler() {
        this.fetchData();
      },
      deep: true,
    },
  },
  created() {
    this.fetchData();
  },
  methods: {
    getAlignClass(align) {
      switch (align) {
        case 'center':
          return 'text-center';
        case 'end':
          return 'text-right';
        default:
          return 'text-left';
      }
    },
    async fetchData() {
      if (this.loading) {
        return;
      }
      this.loading = true;
      this.items = [];
      try {
        const response = await this.axios.get(this.url, { params: this.params });
        const { data, total } = response.data;

        if (data.length > 0 && this.headers.length === 0) {
          this.autoHeaders = Object.keys(data[0]).map((key) => ({
            text: this.formatHeader(key),
            value: key,
          }));
        }

        this.items = data;
        this.totalItems = total;
      } catch (e) {
        this.showSnackbar('error', this.$t('common_error_occurred'));
      } finally {
        this.loading = false;
      }
    },
    formatHeader(key) {
      return key
        .replace(/_/g, ' ')
        .replace(
          /\b\w/g,
          (l) => l.toUpperCase(),
        );
    },
  },
};
</script>
