<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        lg="12"
        xl="12"
        class="px-0"
      >
        <v-layout
          row
          wrap
        >
          <v-col
            md="8"
            sm="12"
            cols="12"
          >
            <v-layout
              row
              justify-start
              wrap
            >
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <text-search
                  v-model="filtering.search.text"
                />
              </v-col>
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <multiselect
                  v-model="status"
                  :items="statusesDisabled"
                  :label="filters.statuses.text"
                  :prepend-icon="filters.statuses.icon"
                  :return-array="true"
                  :disabled="loader"
                  unified
                  dense
                  allow-null
                />
              </v-col>
              <v-col
                md="4"
                sm="12"
                cols="12"
                class="py-1"
              >
                <multiselect
                  v-model="types"
                  :items="typeItems"
                  :label="filters.types.text"
                  :prepend-icon="filters.types.icon"
                  :disabled="loader"
                  dense
                  unified
                  allow-null
                />
              </v-col>
            </v-layout>
          </v-col>
          <v-col
            md="4"
            sm="12"
            cols="12"
          >
            <date-range-picker
              key="dateRangeLoyalAppUsage"
              ref="dateRangeLoyalAppUsage"
              :show-presets="true"
              start-preset="currentMonth"
              @reload-transaction-list="onDateRangeChange"
            />
          </v-col>
        </v-layout>
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="12"
            class="pt-1 pb-1 pr-1 pl-1"
          >
            <v-layout
              row
              wrap
            >
              <v-col
                cols="12"
                sm="12"
                class="display-flex align-center pt-1 pb-1 px-3"
              >
                <v-layout
                  justify-end
                  align-center
                >
                  <btn-refresh
                    class="mt-0"
                    size="small"
                    :disabled="loader"
                    @click="getData"
                  />
                  <report-create-modal
                    btn-class="ml-2"
                    :disabled="loader"
                    :params="exportAsyncParams"
                    :preset="filtering.search.datePreset"
                  />
                </v-layout>
              </v-col>
            </v-layout>
          </v-col>
        </v-layout>
        <v-layout
          row
          wrap
        />
      </v-col>
      <v-col cols="12">
        <div
          v-if="loader"
          class="loader-background"
        />
        <v-data-table
          v-resize="onResize"
          mobile-breakpoint="0"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :loading="loader"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
          :footer-props="filtering.footerProps"
        >
          <template #progress>
            <div class="text-center">
              <v-progress-circular
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <template #item="{ item }">
            <template v-if="!loader">
              <tr>
                <td class="text-sm-start">
                  <div class="flex-inline-start">
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-icon
                          :color="getStatusDetails(item.status).color"
                          v-bind="attrs"
                          v-on="on"
                        >
                          {{ getStatusDetails(item.status).icon }}
                        </v-icon>
                      </template>
                      <span> {{ getStatusDetails(item.status).text }}</span>
                    </v-tooltip>
                    {{ item.id }}
                  </div>
                </td>
                <td class="text-start border-right text-no-wrap">
                  <template v-if="null !== item.time">
                    {{ getDateInUTC(item.time) }}
                  </template>
                </td>
                <td class="text-start">
                  {{ item.userEmail }}
                </td>
                <td class="text-start">
                  {{ item.type }}
                </td>
                <td class="text-end border-right">
                  {{ item.externalId ?? '-' }}
                </td>
                <td class="text-end hidden-sm-and-down md-and-up">
                  {{
                    item.value|currencySymbol(item.currency)
                  }}
                </td>
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import formatDate from 'date-fns/format';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import moment from 'moment';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import SalesDocumentMixin from '@components/common/mixins/SalesDocumentMixin.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import PaymentStatusMixin from '@components/common/badge/icon-text-mixin/PaymentStatusMixin.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import MultiselectValueMixin from '@components/mixins/MultiselectValueMixin.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';

export default {
  components: {
    ReportCreateModal,
    TextSearch,
    Multiselect,
    DateRangePicker,
    BtnRefresh,
  },
  mixins: [
    PaymentStatusMixin,
    SnackbarMixin,
    SalesDocumentMixin,
    MultiselectValueMixin,
  ],
  props: {
    showFiltering: {
      type: Boolean,
      default: true,
    },
    userName: {
      type: String,
      default: null,
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      autoLoadData: this.autoLoad,
      loader: true,
      loaderRefund: false,
      refundPaymentId: null,
      advanceFiltering: this.showFiltering,
      advanced: false,
      pagination: {
        page: 1,
        itemsPerPage: 25,
        sortBy: ['createdAt'],
        sortDesc: [true],
      },
      possibleTypes: [
      ],
      possibleStatuses: [
      ],
      filters: {
        statuses: {
          // icons: {
          //   error: 'mdi-close-octagon-outline',
          //   confirmed: 'mdi-check-circle-outline',
          //   rejected: 'mdi-alert-outline',
          //   initiated: 'mdi-cached',
          //   timeout: 'mdi-clock-outline',
          //   waiting: 'mdi-cached',
          //   refunded: 'mdi-credit-card-refund-outline',
          // },
          // colors: {
          //   error: 'error',
          //   confirmed: 'green darken-2',
          //   rejected: 'error',
          //   initiated: 'progress',
          //   timeout: 'error',
          //   waiting: 'progress',
          //   refunded: 'warning',
          // },
          // texts: {
          //   error: this.$t('common_error'),
          //   confirmed: this.$t('common_paid'),
          //   rejected: this.$t('common_canceled'),
          //   initiated: this.$t('common_processing'),
          //   timeout: this.$t('common_timeout'),
          //   waiting: this.$t('common_processing'),
          //   refunded: this.$t('common_refund'),
          // },
          text: this.$t('common_state'),
          icon: 'mdi-list-status',
        },
        types: {
          icons: {
            p24: 'mdi-bank',
            CREDIT_CARD: 'mdi-credit-card',
          },
          text: this.$t('loyalApp_externalType'),
          icon: 'mdi-cash-multiple',
        },
      },
      types: [
      ],
      status: [
      ],
      filtering: {
        footerProps: {
          'items-per-page-options': [25, 50, 100],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        search: {
          text: null,
          dateFrom: null,
          dateTo: null,
          hourFrom: null,
          hourTo: null,
          datePreset: null,
        },
        nameExists: null,
      },
      dataTable: {
        headers: [
          {
            text: this.$t('common_id'),
            value: 'id',
            sortable: false,
          },
          {
            text: this.$t('common_tableDate'),
            value: 'time',
            displayMethod: 'date',
            sortable: false,
          },
          {
            text: this.$t('common_user'),
            value: 'userEmail',
            class: 'md-and-up',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_accountType'),
            value: 'type',
            class: 'md-and-up',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_externalId'),
            value: 'externalId',
            sortable: false,
            align: 'right',
          },
          {
            text: this.$t('common_value'),
            value: 'value',
            class: 'hidden-sm-and-down md-and-up text-end',
            sortable: false,
          },
        ],
        items: [],
        totalItems: 0,
        sum: 0,
        externalSum: 0,
      },
    };
  },
  computed: ({
    exportAsyncParams() {
      return {
        report: 'v2\\LoyaltyPayments3Report',
        ...this.getParameters(),
      };
    },
    statusesDisabled() {
      const statuses = this.possibleStatuses.map(
        (row) => ({
          text: this.getStatusDetails(row).text ?? row,
          value: row,
          disabled: false,
          icon: this.getStatusDetails(row).icon ?? '',
        }),
      );

      return statuses;
    },
    typeItems() {
      const types = this.possibleTypes.map(
        (row) => ({
          text: row,
          value: row,
          disabled: false,
          icon: this.filters.types.icons[row] ?? '',
        }),
      );

      return types;
    },
  }),
  watch: {
    status() {
      this.pagination.page = 1;
      this.getData();
    },
    types() {
      this.pagination.page = 1;
      this.getData();
    },
    filtering: {
      handler() {
        if (this.autoLoadData) {
          this.getData();
        }
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if ((oldValue.sortDesc !== newValue.sortDesc
                || oldValue.page !== newValue.page
                || oldValue.itemsPerPage !== newValue.itemsPerPage
                || oldValue.sortBy !== newValue.sortBy)
            && this.autoLoadData) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
              || oldValue.sortBy !== newValue.sortBy
              || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          this.getData();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getDateInUTC(date) {
      return moment(date).format('YYYY-MM-DD HH:mm:ss');
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getParameters() {
      return {
        type: this.getMultiselectFlatValue(this.possibleTypes, this.types),
        status: this.getMultiselectFlatValue(this.possibleStatuses, this.status),
        // orderBy: this.pagination.sortBy[0],
        // orderDescending: (this.pagination.sortDesc[0] === true)
        //   ? 1
        //   : 0,
        startDate: this.filtering.search.dateFrom
          ? `${this.filtering.search.dateFrom}`
          : null,
        endDate: this.filtering.search.dateTo
          ? `${this.filtering.search.dateTo}`
          : null,
        search: this.filtering.search.text,
      };
    },
    getData() {
      this.autoLoadData = true;
      this.loader = true;

      this.axios.get(
        '/api/loyalty/v3/payments',
        {
          params:
            {
              ...this.getParameters(),
              page: this.pagination.page,
              perPage: this.pagination.itemsPerPage,
            },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.dataTable.totalItems = Number(response.data.total);
            this.dataTable.items = response.data.data;
            this.possibleTypes = response.data.filters.type;
            this.possibleStatuses = response.data.filters.status;
            this.loader = false;
            this.dataTable.sum = response.data.sum;
            this.dataTable.externalSum = response.data.paymentSum;
          }
        });
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    onDateRangeChange({
      from,
      to,
      value,
    }) {
      this.filtering.search.dateFrom = from;
      this.filtering.search.dateTo = to;
      this.filtering.search.datePreset = value;
    },
    getExportFileName(name, fileType) {
      const today = formatDate(new Date(), 'YYYY-MM-DD');
      const nameArray = [[name, today].join('_'), fileType];

      return nameArray.join('.');
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

.display-flex {
  display: flex;
}

.flex-inline-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-inline-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

</style>
