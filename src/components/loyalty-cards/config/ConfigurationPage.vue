<template>
  <v-container
    fluid
  >
    <v-alert
      v-if="!isOwner"
      text
      border="left"
      type="info"
    >
      {{ $t('loyaltyCards_notOwnerAlert') }}
    </v-alert>
    <v-row>
      <v-col
        cols="12"
        class="text-sm-start pt-0"
      >
        <v-tabs
          slider-color="primary"
          centered
        >
          <v-tab
            v-for="(item) in tabs"
            :key="item.key"
            ripple
            :disabled="!item.show"
          >
            {{ item.text }}
            <v-icon> {{ item.icon }} </v-icon>
          </v-tab>
          <v-tab-item
            v-for="(item) in tabs"
            :key="item.key"
          >
            <component
              :is="item.component"
              v-bind="item.props"
            />
          </v-tab-item>
        </v-tabs>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { mapGetters } from 'vuex';
import loadingOverlay from '@components/common/LoadingOverlay.vue';
import OwnerConfigEdit from '@components/loyalty-cards/config/companyDataConfig/OwnerConfigEdit.vue';
import InvoiceConfig from '@components/loyalty-cards/config/invoiceConfig/InvoiceConfig.vue';
import PaymentsConfiguration
  from '@components/loyalty-cards/config/paymentsConfig/PaymentsConfiguration.vue';

export default {
  components: {
    OwnerConfigEdit,
    loadingOverlay,
    InvoiceConfig,
    PaymentsConfiguration,
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
      hasRole: 'auth/hasRole',
    }),
    tabs() {
      return [
        {
          component: InvoiceConfig,
          icon: 'mdi-invoice-text-edit',
          key: 'invoiceConfig',
          show: true,
          text: this.$t('loyaltyCards_invoicing'),
        },
        {
          component: OwnerConfigEdit,
          icon: 'mdi-briefcase-edit-outline',
          key: 'ownerConfigEdit',
          show: true,
          text: this.$t('common_companyData'),
        },
        {
          component: PaymentsConfiguration,
          icon: 'mdi-currency-usd',
          key: 'paymentsConfiguration',
          show: true,
          text: this.$t('loyaltyCards_payments'),
        },
      ];
    },
  },
};
</script>
