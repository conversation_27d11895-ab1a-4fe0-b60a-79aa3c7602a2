<template>
  <v-data-table
    item-key="id"
    mobile-breakpoint="0"
    :headers="headers"
    :items="tableItems"
    :loading="loader"
    sort-by="createdAt"
    :sort-desc="true"
    :footer-props="footerProps"
    :options="options"
    :server-items-length="itemsTotal"
    @update:options="onOptionsChange"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item }">
      <template v-if="!loader">
        <tr
          :key="item.id"
          class="text-sm-start"
        >
          <td class="text-start">
            {{ item?.cardNumber }}
          </td>
          <td class="text-end">
            {{ item?.cardAlias }}
          </td>
          <td class="text-end border-right">
            {{ item?.cardClientName ?? '-' }}
          </td>
          <td class="text-end">
            {{ item.comment }}
          </td>
          <td class="text-start border-right">
            <v-icon
              v-if="item.isActive"
            >
              mdi-check
            </v-icon>
            <v-icon
              v-else
            >
              mdi-close
            </v-icon>
          </td>
          <td class="text-start">
            {{ item.startTime|formatDateDay }}
          </td>
          <td class="text-start border-right">
            {{ item.endTime|formatDateDay }}
          </td>
          <td class="text-end">
            {{ getTypeName(item.type) }}
          </td>
          <td class="text-center border-right">
            <template v-if="item.lastCall !== null">
              {{ item.lastCall|formatDateDay }}
            </template>
            <template v-else>
              -
            </template>
          </td>
          <custom-currency-symbol-cell
            :value="parseFloat(item.value)"
            :currency="item.cardCurrencySymbol"
          />
          <td class="text-end">
            <v-btn
              :key="`editCyclicConfigButton${item.id}`"
              x-small
              tile
              rounded
              fab
              class="ml-2"
              color="primary"
              elevation="1"
              @click="openModal(
                `editCyclicConfigDialog${item.id}`,
                { configId: item.id }
              )"
            >
              <v-icon>mdi-pencil</v-icon>
            </v-btn>
          </td>
        </tr>
      </template>
      <cyclic-top-up-add-edit-modal
        :key="`editCyclicConfigDialog${item.id}`"
        :ref="`editCyclicConfigDialog${item.id}`"
        :config-id="item.id"
        @cyclic-config-edited="emitReload()"
      />
    </template>
  </v-data-table>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import CyclicTopUpAddEditModal from './CyclicTopUpAddEditModal.vue';

export default {
  name: 'CuclicTopUpTable',
  components: {
    CustomCurrencySymbolCell,
    CyclicTopUpAddEditModal,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    options: {
      type: Object,
      default() {
        return {};
      },
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    sums: {
      type: Object,
      required: true,
    },
    itemsTotal: {
      type: Number,
      default: -1,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      filtering: {
        options: {},
      },
      footerProps: {
        'items-per-page-options': [5, 10, 15, 25],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      headers: [
        {
          value: 'cardNumber',
          text: this.$t('loyaltyCards_card'),
        },
        {
          value: 'cardAlias',
          text: this.$t('loyaltyCards_name'),
          align: 'end',
        },
        {
          value: 'client',
          text: this.$t('common_client'),
          align: 'end',
        },
        {
          value: 'comment',
          text: this.$t('common_comment'),
          align: 'end',
        },
        {
          value: 'isActive',
          text: this.$t('loyaltyCards_state'),
        },
        {
          value: 'startTime',
          text: this.$t('loyaltyCards_startTime'),
        },
        {
          value: 'endTime',
          text: this.$t('loyaltyCards_endTime'),
        },
        {
          value: 'type',
          text: this.$t('loyaltyCards_type'),
          align: 'end',
        },
        {
          value: 'lastCal',
          text: this.$t('loyaltyCards_lastCal'),
          align: 'center',
        },
        {
          value: 'value',
          text: this.$t('loyaltyCards_value'),
          align: 'end',
        },
        {
          value: 'actions',
          text: this.$t('actions.actions'),
          align: 'end',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }

      return this.items;
    },
  },
  methods: {
    onOptionsChange(options) {
      this.$set(this.filtering, 'options', options);
      setTimeout(() => {
        this.$emit('reload-cyclic-confi-list', {});
      }, 200);
    },
    emitReload() {
      this.$emit('reload-cyclic-confi-list', {});
    },
    getTypeName(type) {
      if (type === 'ADD') {
        return this.$t('loyaltyCards_cyclicAdd');
      }

      if (type === 'ALIGN') {
        return this.$t('loyaltyCards_cyclicAlign');
      }

      return this.$t('loyaltyCards_cyclicAlign');
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
  },
};
</script>

<style>

.no-wrap {
  white-space: nowrap;
}

</style>
