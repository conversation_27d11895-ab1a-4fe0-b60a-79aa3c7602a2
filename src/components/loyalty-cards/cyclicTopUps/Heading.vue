<template>
  <h2>
    <span>
      {{ $t('common_cyclicTopUpsHeading') }}
    </span>
  </h2>
</template>

<script>

export default {
  name: 'CyclicTopUpsHeading',
  props: {
    dates: {
      type: Object,
      required: true,
    },
  },
  computed: {
    dateFrom() {
      return this.$options.filters.formatDateDay(this.dates.from);
    },
    dateTo() {
      return this.$options.filters.formatDateDay(this.dates.to);
    },
  },
};
</script>
