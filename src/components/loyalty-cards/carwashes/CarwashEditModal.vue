<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      scrollable
      width="800"
      style="z-index: 1200;"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('common_carwash') }}
            </h5>
          </span>
        </v-card-title>
        <v-progress-linear
          v-if="loaders.site"
          :indeterminate="true"
          class="mt-0"
        />
        <v-card-text class="pt-6">
          <div class="text-center">
            <v-progress-circular
              v-if="loaders.site"
              class="circleProgress"
              :size="90"
              :width="7"
              color="primary"
              indeterminate
            />
          </div>
          <template v-if="!loaders.site">
            <v-container grid-list-md>
              <v-form
                ref="formClientEdit"
                v-model="form.valid"
                lazy-validation
              >
                <v-layout wrap>
                  <v-col
                    cols="12"
                  >
                    <v-switch
                      v-model="carwash.showOnMap"
                      :dense="false"
                    >
                      <template #label>
                        <div class="pl-3 py-0">
                          {{ $t('loyalApp_showOnMap') }}
                        </div>
                      </template>
                    </v-switch>
                  </v-col>
                  <v-col
                    cols="12"
                  >
                    <v-textarea
                      v-model="carwash.description"
                      prepend-icon="mdi-text"
                      :label="$t('common_tableDescription')"
                      class="descriptionText"
                      auto-grow
                      rows="1"
                    />
                  </v-col>
                </v-layout>
              </v-form>
            </v-container>
          </template>
        </v-card-text>
        <v-card-actions v-if="!loaders.site">
          <v-spacer />
          <v-btn
            color="gray"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loaders.actualize"
            :disabled="!form.valid"
            @click.native="submit"
          >
            {{ $t('actions.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>
<script>

export default {
  props: {
    carwashId: {
      type: Number,
      default: null,
    },
    carwashData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      loaders: {
        site: true,
        actualize: false,
      },
      dialog: false,
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      rules: {
        required: [(v) => !!v || this.$t('common_fieldRequired')],
        selectRequired2: [(v) => (v !== null && v.length > 0) || this.$t('common_fieldRequired')],
      },
    };
  },
  computed: {
    carwash() {
      if (typeof this.carwashData === 'undefined') {
        return {};
      }

      return this.carwashData;
    },
  },
  watch: {
    dialog(val) {
      if (val) {
        // Reset form and validation state before showing modal
        this.$nextTick(() => {
          this.resetValidationErrorsAndClearFields();
          this.loaders.actualize = false;
          this.loaders.site = false;

          // this.getData();
        });
      } else {
        // Clean up when modal is closed
        this.resetValidationErrorsAndClearFields();
      }
    },
  },
  beforeDestroy() {
    this.resetValidationErrorsAndClearFields();
  },
  methods: {
    validateAll() {
      return this.$validator.validateAll();
    },
    resetAndValidate() {
      this.$validator.reset();
      return this.validateAll();
    },
    resetValidationErrorsAndClearFields() {
      // this.clearFormData();
      if (this.$refs.formCarwashEdit) {
        this.$refs.formCarwashEdit.reset();
        this.$refs.formCarwashEdit.resetValidation();
      }
      // this.$validator.reset();
      this.$nextTick(() => {
        if (this.$refs.formCarwashEdit) {
          this.$refs.formCarwashEdit.validate();
        }
      });
    },
    onError() {
      this.$emit('update-fail');
      // on error
      this.closeDialog();
    },
    submit() {
      this.loaders.actualize = true;
      this.loaders.site = true;

      const params = {
        showOnMap: this.carwash.showOnMap,
        description: this.carwash.description,
      };

      this.axios.patch(
        `/api/loyalty/carwash/${this.carwash.id}`,
        params,
      )
        .then(
          (response) => {
            if ((response.status === 200) && response.data) {
              this.loaders.actualize = false;
              this.loaders.site = false;
              this.onSuccess();
            }
          },
          () => {
            this.onError();
            this.loaders.actualize = false;
            this.loaders.site = false;
          },
        );
    },
    clearFormData() {
      this.carwash = {
        desctiprion: null,
        cashback: true,
      };
    },
    propagateClientUpdate() {
      this.$emit('on-client-update');
    },
    closeDialog() {
      this.dialog = false;
      this.$nextTick(() => {
        this.resetValidationErrorsAndClearFields();
      });
    },
    onSuccess() {
      this.$emit('update-success');
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
  },
};
</script>

<style scoped>
.card-text-wrap {
  display: flex;
  flex-direction: column;
}

.card-text-wrap p {
  font-size: 18px;
  text-align: center;
}

.descriptionText {
  font-size: 12px;
}
</style>
