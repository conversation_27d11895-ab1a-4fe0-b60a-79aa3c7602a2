<template>
  <v-layout
    row
    justify-center
  >
    <loading-overlay v-if="uploading" />
    <v-dialog
      v-model="dialog"
      content-class="dialogWidth-3"
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">{{ $t('loyaltyCards_rechargeMultiple') }}</h5>
          </span>
          <v-spacer />
          <v-btn
            icon
            text
            tile
            small
            dark
            @click="closeDialog"
          >
            <v-icon>
              mdi-close
            </v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text class="pt-6 text-body-1">
          <div
            v-if="topUpErrors.length != 0"
            class="pb-3 error--text"
          >
            <p class="font-weight-bold">
              {{ $t('loyaltyCards_lastFileUploadErrors') }}:
            </p>
            <v-simple-table
              dense
              class="elevation-3 error--text scrollable-table"
            >
              <template #default>
                <thead>
                  <tr>
                    <th class="text-left error--text">
                      {{ $t('loyaltyCards_fileLine') }}
                    </th>
                    <th class="text-left error--text">
                      {{ $t('loyaltyCards_errorDescription') }}
                    </th>
                    <th class="text-left error--text">
                      {{ $t('loyaltyCards_errorDetails') }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="[key, error] in topUpErrors"
                    :key="`item-${key}`"
                  >
                    <td>
                      {{ error.place }}
                    </td>
                    <td>
                      {{ error.description }}
                    </td>
                    <td>
                      {{ error.content }}
                    </td>
                  </tr>
                </tbody>
              </template>
            </v-simple-table>
          </div>
          <v-form
            ref="form"
            v-model="form.valid"
            :lazy-validation="true"
          >
            <v-container
              fluid
              class="py-0 px-0"
            >
              <p class="font-weight-bold">
                {{ $t('loyaltyCards_rechargeInfoHint') }}
              </p>
              <span>
                {{ $t('loyaltyCards_rechargeHint') }}
              </span>
              <input
                id="file"
                ref="file"
                type="file"
                @change="submitFiles();"
              >
            </v-container>
          </v-form>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn
            color="secondary"
            @click="addFile();"
          >
            {{ $t('actions.send_file') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>
import LoadingOverlay from '@components/common/LoadingOverlay.vue';

export default {
  components: { LoadingOverlay },
  data() {
    return {
      dialog: false,
      file: '',
      uploading: false,
      topUpErrors: [],
      card: {
        number: '',
        name: '',
        client: null,
        credits: '',
      },
      clients: [],
      clientsOptions: [],
      form: {
        validateOnBlur: true,
        valid: true,
      },
    };
  },
  methods: {
    closeDialog() {
      this.topUpErrors = [];
      this.form.valid = true;
      this.dialog = false;
    },
    submitFiles() {
      [this.file] = this.$refs.file.files;
      const formData = new FormData();
      formData.append('file', this.file);
      this.uploading = true;

      this.axios.post(
        '/api/loyalty/bulk/top_up/csv',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      )
        .then(
          () => {
            this.uploading = false;

            // this.$eventHub.$emit(
            //   'hide-error',
            // );
            //
            // this.$eventHub.$emit(
            //   'show_application_success',
            //   this.$t('loyaltyCards_topUpActionSucceed'),
            //   'published_with_changes',
            // );

            this.closeDialog();
            this.$refs.file.value = null;
            this.$emit('reload-card-list');
          },
        )
        .catch((error) => {
          let status;
          this.uploading = false;

          if (error.response) {
            status = error.response.status;
            if (status === 405 || status === 404 || status === 400) {
              this.topUpErrors = Object.entries(error.response.data.errors);
            }
          }
          this.$refs.file.value = null;
        });
    },
    addFile() {
      this.topUpErrors = [];
      this.$refs.file.click();
    },
  },
};
</script>

<style>
input[type="file"] {
  position: absolute;
  top: -500px;
  width: 1px;
}

.scrollable-table {
  max-height: 300px; /* Set the desired height for the scrollable card */
  overflow-y: auto; /* Enable vertical scrolling */
}
</style>
