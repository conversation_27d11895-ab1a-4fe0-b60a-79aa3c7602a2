<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <template v-if="showFiltering">
      <v-row>
        <v-col>
          <text-search
            v-model="filtering.search.text"
          />
        </v-col>
        <v-col>
          <multiselect
            v-model="filtering.topUpInvoiceExists"
            unified
            :items="topUpInvoiceExists"
            :disabled="loader"
            prepend-icon="mdi-file-outline"
            :label="$t('common_invoice')"
          />
        </v-col>
        <v-col>
          <date-range-picker
            key="dateRange"
            ref="dateRange"
            prepend-icon="mdi-calendar-range"
            :show-presets="true"
            :show-custom="true"
            :disabled="loader"
            @reload-transaction-list="onDateRangeChange"
          />
        </v-col>
      </v-row>
      <v-row class="mt-0">
        <v-col
          class="py-0"
        >
          <multiselect
            v-model="filtering.topUpStatus"
            :items="topUpStatuses"
            :disabled="loader"
            prepend-icon="mdi-state-machine"
            :label="$t('common_state')"
          />
        </v-col>
        <v-col
          class="py-0"
        >
          <multiselect
            v-model="filtering.sources"
            :items="allowedSources"
            :label="$t('loyaltyCards_source')"
            :return-array="true"
            :disabled="loader"
            prepend-icon="mdi-point-of-sale"
            unified
            allow-null
          />
        </v-col>
        <v-col
          class="py-0"
        >
          <multiselect
            v-model="filtering.types"
            :items="allowedTypes"
            :label="$t('fiscal_transactions.table.type')"
            :disabled="loader"
            :return-array="true"
            prepend-icon="mdi-cash-multiple"
            unified
            allow-null
          />
        </v-col>
      </v-row>
    </template>

    <v-row>
      <v-col
        cols="12"
        sm="8"
        class="text-sm-start"
      >
        <h2>
          <span>{{ $t('loyaltyCards_loyaltyTopupsHistoryHeading') }}</span>
        </h2>
      </v-col>
      <v-col
        cols="12"
        sm="4"
        class="d-flex justify-end"
      >
        <btn-refresh
          class="mr-2"
          @click="getDataDebounced"
        />
        <report-create-modal
          btn-class="ml-2"
          :params="exportAsyncParams"
          :disabled="loader"
          :preset="filtering.search.dateValue"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        class="px-0"
      >
        <div
          v-if="loader"
          class="loader-background"
        />
        <v-data-table
          key="loyaltyTransactionsTable"
          v-resize="onResize"
          mobile-breakpoint="0"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :loading="loader"
          :options.sync="pagination"
          :single-expand="singleExpand"
          :server-items-length="dataTable.totalItems"
          :expanded.sync="expanded"
          :footer-props="dataTable.footerProps"
        >
          <template #progress>
            <div class="text-center mx-n4">
              <v-progress-linear
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>
          <template #item="{ item }">
            <template v-if="!loader">
              <tr>
                <td class="text-sm-start">
                  <span v-if="item.ctime">
                    {{ item.ctime|formatDateDayTime }}
                  </span>
                  <span v-else>-</span>
                </td>
                <td class="text-sm-start">
                  {{ item.cardNumber }}
                </td>
                <td
                  class="hidden-sm-and-down md-and-up text-sm-start"
                >
                  {{ item.cardAlias || '–' }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-start">
                  <span v-if="item.cardClientId">{{ item.cardClientName }}</span>
                  <span v-else>–</span>
                </td>
                <td class="text-sm-start">
                  <div class="d-flex justify-end">
                    <progress-badge
                      :value="item.progress"
                      :tooltip="true"
                    />
                    <device-type-badge
                      :device-type="item.source"
                      :tooltip="true"
                    />
                  </div>
                </td>
                <td class="text-sm-start border-right">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <v-icon
                        v-bind="attrs"
                        v-on="on"
                      >
                        {{ getPaymentTypeDetails(item.type).icon }}
                      </v-icon>
                    </template>
                    <span>{{ getPaymentTypeDetails(item.type).text }}</span>
                  </v-tooltip>
                </td>
                <td class="text-end hidden-sm-and-down md-and-up">
                  {{ item.topUpSent|currencySymbol(item.currencySymbol) }}
                </td>
                <td class="text-end hidden-sm-and-down md-and-up">
                  {{ item.topUpToSend|currencySymbol(item.currencySymbol) }}
                </td>
                <td class="text-end border-right">
                  {{ item.topUpValue|currencySymbol(item.currencySymbol) }}
                </td>
                <td class="hidden-sm-and-down md-and-up text-sm-center">
                  <template v-if="item.addedBy">
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-icon
                          v-bind="attrs"
                          v-on="on"
                        >
                          mdi-account
                        </v-icon>
                      </template>
                      <span>
                        {{ $t('loyaltyCards_addedBy') }} {{ item.addedBy }}
                      </span>
                    </v-tooltip>
                  </template>
                  <template v-else>
                    –
                  </template>
                </td>
                <td class="text-end">
                  {{ item.invoiceNumber ?? '-' }}
                </td>
                <td>
                  <v-col
                    class="d-flex justify-end pt-1 pb-1"
                  >
                    <template v-if="item.invoice">
                      <act-download
                        :url="`/api/gateway/bkfpay-owner/invoice/${item.invoice}/download`"
                      />
                      <top-up-send-invoice-modal
                        v-if="item.cardClientId !== null"
                        :invoice-id="item.invoice"
                        x-small
                        :client-id="item.cardClientId"
                      />
                    </template>
                    <top-up-generate-invoice-modal
                      v-else-if="item.invoiceGenerate"
                      :id="item.id"
                      :client-id="item.cardClientId"
                      @generateSuccess="getData"
                    />
                  </v-col>
                </td>
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<!--suppress ChainedFunctionCallJS -->
<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import DeviceTypeBadge from '@components/common/badge/DeviceTypeBadge.vue';
import debounce from 'lodash/debounce';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import ProgressBadge from '@components/common/badge/ProgressBadge.vue';
import ProgressMixin from '@components/common/badge/icon-text-mixin/ProgressMixin.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import TransactionTypeMixin from '@components/common/badge/icon-text-mixin/TransactionTypeMixin.vue';
import DeviceTypeMixin from '@components/common/badge/icon-text-mixin/DeviceTypeMixin.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';
import TopUpGenerateInvoiceModal from './TopUpGenerateInvoiceModal.vue';
import TopUpSendInvoiceModal from './TopUpSendInvoiceModal.vue';

export default {
  components: {
    ActDownload,
    ReportCreateModal,
    BtnRefresh,
    DateRangePicker,
    DeviceTypeBadge,
    Multiselect,
    ProgressBadge,
    TextSearch,
    TopUpGenerateInvoiceModal,
    TopUpSendInvoiceModal,
  },
  mixins: [
    DeviceTypeMixin,
    ExportMixin,
    ProgressMixin,
    SnackbarMixin,
    TransactionTypeMixin,
  ],
  props: {
    showFiltering: {
      type: Boolean,
      default: true,
    },
    cardNumber: {
      type: String,
      default: null,
    },
    cardToken: {
      type: String,
      default: null,
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    autoUpdateTransactions: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      possibleSources: [],
      possibleTypes: [],
      summary: {
        topUpSent: 0,
        topUpToSend: 0,
        topUpValue: 0,
      },
      singleExpand: true,
      expanded: [],
      autoLoadData: this.autoLoad,
      registerUserNameEvent: this.autoUpdateTransactions,
      cardNum: this.cardNumber,
      cardTok: this.cardToken,
      loader: true,
      advanceFiltering: this.showFiltering,
      advanced: false,
      pagination: {
        page: 1,
        itemsPerPage: 25,
        sortBy: ['createdAt'],
        sortDesc: [true],
      },
      filtering: {
        search: {
          text: null,
          dateFrom: null,
          dateTo: null,
          dateValue: null,
          hourFrom: null,
          hourTo: null,
        },
        topUpStatus: [],
        types: [],
        sources: [],
        nameExists: null,
        topUpInvoiceExists: null,
      },
      dataTable: {
        footerProps: {
          'items-per-page-options': [5, 10, 15, 25],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('common_tableDate'),
            value: 'createdAt',
            showInRowExpand: true,
            displayMethod: 'date',
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_number'),
            value: 'cardNumber',
            class: 'md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_name'),
            value: 'cardName',
            class: 'hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_client'),
            value: 'cardClientName',
            class: 'hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_sourceStatus'),
            value: 'progress',
            class: 'md-and-up',
            displayMethod: 'top-up-progress',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('fiscal_transactions.table.type'),
            value: 'type',
            class: 'md-and-up',
            displayMethod: 'top-up-progress',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_topupSent'),
            value: 'topUpToSend',
            class: 'hidden-sm-and-down md-and-up text-end',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_topUpsToSent'),
            value: 'topUpSent',
            class: 'hidden-sm-and-down md-and-up text-end',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_topup'),
            value: 'topUpValue',
            class: 'text-end',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_addedBy'),
            value: 'addedBy',
            class: 'hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_invoiceNumber'),
            value: 'invoiceNumber',
            align: 'end',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_invoice'),
            value: 'invoice',
            class: 'text-end',
            showInRowExpand: true,
            sortable: false,
          },
        ],
        items: [],
        totalItems: 0,
      },
      topUpStatuses: [
        {
          text: this.$t('common_toSent'),
          value: 'WAITING',
          icon: 'mdi-timer-sand-empty',
        },
        {
          text: this.$t('common_notFullySent'),
          value: 'NOT_FULLY_REFILLED',
          icon: 'mdi-autorenew',
        },
        {
          text: this.$t('common_sendToCard'),
          value: 'REFILLED',
          icon: 'mdi-check-underline',
        },
      ],
      topUpInvoiceExists: [
        {
          text: this.$t('loyaltyCards_withInvoice'),
          value: 1,
          icon: 'mdi-file-outline',
        },
        {
          text: this.$t('loyaltyCards_withoutInvoice'),
          value: 0,
          icon: 'mdi-file-remove-outline',
        },
      ],
      clientInvoiceDataDialog: false,
    };
  },
  computed: {
    allowedSources() {
      const sources = this.possibleSources.map(
        (row) => ({
          text: this.getTopUpSourceIcon(row).text ?? row,
          value: row,
          disabled: false,
          icon: this.getTopUpSourceIcon(row).icon ?? '',
        }),
      );
      return sources;
    },
    allowedTypes() {
      const types = this.possibleTypes.map(
        (row) => ({
          text: this.getPaymentTypeDetails(row).text ?? row,
          value: row,
          disabled: false,
          icon: this.getPaymentTypeDetails(row).icon ?? '',
        }),
      );
      return types;
    },
    exportAsyncParams() {
      return {
        report: 'v2\\LoyaltyTopUps3Report',
        // orderBy: this.pagination.sortBy[0],
        // orderDescending: Number(this.pagination.sortDesc[0] === true),
        cardToken: this.cardTok,
        status: this.getToggleTopUpStatusFiltering(),
        source: this.getToggleTopUpSourceFiltering(),
        type: this.getToggleTopUpTypeFiltering(),
        startDate: this.filtering.search.dateFrom && this.cardNum == null
          ? this.filtering.search.dateFrom
          : null,
        endDate: this.filtering.search.dateTo && this.cardNum == null
          ? this.filtering.search.dateTo
          : null,
        search: this.filtering.search.text,
        invoiceExists: this.filtering.topUpInvoiceExists,
      };
    },
  },
  watch: {
    filtering: {
      handler() {
        if (this.autoLoadData) {
          this.pagination.page = 1;
          this.getDataDebounced();
        }
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if ((oldValue.sortDesc !== newValue.sortDesc
            || oldValue.page !== newValue.page
            || oldValue.itemsPerPage !== newValue.itemsPerPage
            || oldValue.sortBy !== newValue.sortBy)
          && this.autoLoadData) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          this.getDataDebounced();
        }
      },
      deep: true,
    },
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.getData();
    }, 800);
  },
  mounted() {
    // show one card when there is card in url
    if (this.$route.query.card) {
      this.filtering.search.text = this.$route.query.card;
    }
  },
  methods: {
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getToggleTopUpSourceFiltering() {
      return this.filtering.sources.length ? this.filtering.sources.join(',') : null;
    },
    getToggleTopUpStatusFiltering() {
      return this.filtering.topUpStatus.length ? this.filtering.topUpStatus.join(',') : null;
    },
    getToggleTopUpTypeFiltering() {
      return this.filtering.types.length ? this.filtering.types.join(',') : null;
    },
    getData() {
      this.autoLoadData = true;
      this.loader = true;
      this.axios.get(
        '/api/loyalty/v3/top_ups',
        {
          params: {
            page: this.pagination.page,
            // orderBy: this.pagination.sortBy[0],
            // orderDescending: Number(this.pagination.sortDesc[0] === true),
            perPage: this.pagination.itemsPerPage,
            cardToken: this.cardTok,
            status: this.getToggleTopUpStatusFiltering(),
            source: this.getToggleTopUpSourceFiltering(),
            type: this.getToggleTopUpTypeFiltering(),
            startDate: this.filtering.search.dateFrom && this.cardNum == null
              ? this.filtering.search.dateFrom
              : null,
            endDate: this.filtering.search.dateTo && this.cardNum == null
              ? this.filtering.search.dateTo
              : null,
            search: this.filtering.search.text,
            invoiceExists: this.filtering.topUpInvoiceExists,
          },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.dataTable.totalItems = Number(response.data.total);
            this.dataTable.items = response.data.data.map((item) => ({
              ...item,
              loading: false,
            }));
            this.possibleSources = response.data.filters.source;
            this.possibleTypes = response.data.filters.type;

            // this.summary = response.data.summary;
            this.loader = false;
          }
        });
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    onDateRangeChange({
      from,
      to,
      value,
    }) {
      this.filtering.search.dateFrom = from;
      this.filtering.search.dateTo = to;
      this.filtering.search.dateValue = value;
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

</style>
