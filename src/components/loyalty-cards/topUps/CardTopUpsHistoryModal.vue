<template>
  <v-row justify="center">
    <v-dialog
      v-model="dialog"
      fullscreen
      hide-overlay
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ cardTitle }}
            </h5>
          </span>
          <v-spacer />
          <v-btn
            icon
            text
            tile
            small
            dark
            @click="dialog = false"
          >
            <v-icon>
              mdi-close
            </v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <card-top-ups-history-list
            :ref="`topUpsHistoryListModal${card.number}`"
            :auto-update-transactions="false"
            :autoload-data="false"
            :card-number="card.number"
            :card-token="card.token"
            :show-filtering="false"
          />
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-row>
</template>

<script>

import CardTopUpsHistoryList from '@components/loyalty-cards/topUps/LoyaltyTopUpsHistoryList.vue';

export default {
  components: {
    CardTopUpsHistoryList,
  },
  props: {
    cardNumber: {
      type: String,
      default: null,
    },
    cardToken: {
      type: String,
      default: null,
    },
    cardName: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      card: {
        number: this.cardNumber,
        name: this.cardName,
        token: this.cardToken,
      },
      dialog: false,
    };
  },
  computed: {
    cardTitle() {
      if (this.card.name) {
        return `${this.$t('transactions.topup_card_history')} - ${this.card.number} (${this.card.name})`;
      }
      return `${this.$t('transactions.topup_history')} - ${this.card.number}`;
    },
  },
  watch: {
    dialog() {
      // get card data only when dialog shows up
      if (this.dialog) {
        setTimeout(() => {
          this.getTransactionsData();
        }, 1000);
      }
    },
  },
  methods: {
    getTransactionsData() {
      this.$refs[`topUpsHistoryListModal${this.card.number}`].getData();
    },
  },
};
</script>
