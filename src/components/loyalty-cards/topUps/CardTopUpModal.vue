<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      scrollable
      persisten
      content-class="dialogWidth-3"
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('actions.refill_card') }} {{ card.number }}
            </h5>
          </span>
        </v-card-title>
        <v-progress-linear
          v-if="loaders.site"
          :indeterminate="true"
          class="mt-0"
        />
        <v-card-text class="pt-6">
          <v-progress-circular
            v-if="loaders.site"
            class="circleProgress"
            :size="90"
            :width="7"
            color="primary"
            indeterminate
          />
          <template v-if="!loaders.site">
            <v-expansion-panels
              v-if="notFullySentTopUpsList.length > 0 || cyclicTopUpsHistory.length > 0"
            >
              <v-expansion-panel>
                <v-expansion-panel-header>
                  <h3>{{ $t('loyaltyCards_activeTopups') }}:</h3>
                  {{ $t('common_toSent') }}: {{ notFullySentTopUpsList.length }}
                  {{ $t('loyaltyCards_cyclic') }}: {{ cyclicTopUpsHistory.length }}
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <h2>{{ $t('loyaltyCards_notSentList') }}</h2>
                  <v-simple-table>
                    <template #default>
                      <thead>
                        <tr>
                          <th class="text-left">
                            {{ $t('common_tableDate') }}
                          </th>
                          <th class="text-left">
                            {{ $t('loyaltyCards_sourceStatus') }}
                          </th>
                          <th class="text-left">
                            {{ $t('fiscal_transactions.table.type') }}
                          </th>
                          <th class="text-right">
                            {{ $t('common_topupSent') }}
                          </th>
                          <th class="text-right">
                            {{ $t('loyaltyCards_topUpsToSent') }}
                          </th>
                          <th class="text-right">
                            {{ $t('loyaltyCards_topup') }}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="(notSentTopUp, id) in notFullySentTopUpsList"
                          :key="id"
                        >
                          <td>
                            {{ notSentTopUp.ctime|formatDateDayTime }}
                          </td>
                          <td>
                            <span class="d-flex">
                              <progress-badge
                                :value="notSentTopUp.progress"
                                :tooltip="true"
                              />
                              <device-type-badge
                                :device-type="notSentTopUp.source"
                                :tooltip="true"
                              />
                            </span>
                          </td>
                          <td>
                            <v-tooltip bottom>
                              <template #activator="{ on, attrs }">
                                <v-icon
                                  v-bind="attrs"
                                  v-on="on"
                                >
                                  {{ getPaymentTypeDetails(notSentTopUp.type).icon }}
                                </v-icon>
                              </template>
                              <span>{{ getPaymentTypeDetails(notSentTopUp.type).text }}</span>
                            </v-tooltip>
                          </td>
                          <td class="text-end">
                            {{ notSentTopUp.topUpSent|currencySymbol(
                              notSentTopUp.currencySymbol)
                            }}
                          </td>
                          <td class="text-end">
                            {{ notSentTopUp.topUpToSend|currencySymbol(
                              notSentTopUp.currencySymbol)
                            }}
                          </td>
                          <td class="text-end">
                            {{ notSentTopUp.topUpValue|currencySymbol(
                              notSentTopUp.currencySymbol)
                            }}
                          </td>
                        </tr>
                      </tbody>
                    </template>
                  </v-simple-table>
                  <template
                    v-if="canAccess('loyalty', 'cyclicTopUps') || true"
                  >
                    <h2 class="mt-4">
                      {{ $t('loyaltyCards_cyclicTopUpList') }}
                    </h2>
                    <v-simple-table>
                      <template #default>
                        <thead>
                          <tr>
                            <th class="text-left">
                              {{ $t('loyaltyCards_startTime') }}
                            </th>
                            <th class="text-left">
                              {{ $t('loyaltyCards_endTime') }}
                            </th>
                            <th class="text-left">
                              {{ $t('loyaltyCards_type') }}
                            </th>
                            <th class="text-left">
                              {{ $t('loyaltyCards_value') }}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            v-for="(cyclicTopUpTmp, id) in cyclicTopUpsHistory"
                            :key="id"
                          >
                            <td>{{ cyclicTopUpTmp.startTime|formatDateDay }}</td>
                            <td>{{ cyclicTopUpTmp.endTime|formatDateDay }}</td>
                            <td>{{ getTypeName(cyclicTopUpTmp.type) }}</td>
                            <td>
                              {{ cyclicTopUpTmp.value }} {{ cyclicTopUpTmp.cardCurrencySymbol }}
                            </td>
                          </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </template>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
            <v-form
              ref="formCardRefill"
              v-model="forms.refill.valid"
              lazy-validation
            >
              <v-row>
                <v-col
                  class="pt-0"
                  cols="12"
                >
                  <v-radio-group
                    v-model="topUpType"
                    dense
                    :label="$t('loyaltyCards_topUpType')"
                  >
                    <v-radio
                      :label="$t('loyaltyCards_oneTime')"
                      value="one_time"
                    />
                    <v-radio
                      :label="$t('loyaltyCards_cyclicAdd')"
                      value="cyclic_add"
                    />
                    <v-radio
                      :label="$t('loyaltyCards_cyclicAlign')"
                      value="cyclic_align"
                    />
                  </v-radio-group>
                </v-col>
              </v-row>
              <v-row>
                <v-col
                  cols="3"
                >
                  <v-text-field
                    v-model="card.refillValue"
                    prepend-icon="mdi-trending-up"
                    :label="amountLabel"
                    :rules="[
                      forms.validationRules.positiveNumber,
                      forms.validationRules.topupValue
                    ]"
                    @change="calculateBonus('value')"
                  />
                </v-col>
                <v-col
                  cols="3"
                >
                  <v-text-field
                    v-model="card.discountPercent"
                    :label="$t('discountPercentage')"
                    prepend-icon="mdi-percent-outline"
                    :rules="[
                      forms.validationRules.nonNegativeNumber,
                      forms.validationRules.topupValue
                    ]"
                    @change="calculateBonus('value')"
                  />
                </v-col>
                <v-col
                  cols="3"
                >
                  <v-text-field
                    v-model="card.bonusValue"
                    disabled
                    :label="$t('discountValue')"
                    :rules="[
                      forms.validationRules.nonNegativeNumber,
                      forms.validationRules.topupValue
                    ]"
                    @change="calculateBonus('percent')"
                  />
                </v-col>
                <v-col
                  cols="3"
                >
                  <v-text-field
                    v-model="card.toPay"
                    disabled
                    :label="$t('amountToPay')"
                    :rules="[
                      forms.validationRules.nonNegativeNumber,
                      forms.validationRules.topupValue
                    ]"
                  />
                </v-col>
              </v-row>
              <v-row v-show="isCyclicTopUp">
                <v-col
                  cols="6"
                  class="pt-0 pb-0"
                >
                  <date-range-picker
                    key="dateRange"
                    ref="dateRange"
                    prepend-icon="mdi-calendar-range"
                    :show-presets="false"
                    :start-date-range="[
                      cyclicTopUp.dateFrom,
                      cyclicTopUp.dateTo
                    ]"
                    :date-maximum="maxDate()"
                    @reload-transaction-list="onDateRangeChange"
                  />
                </v-col>
                <v-col
                  cols="6"
                  class="pt-0 pb-0"
                >
                  <v-checkbox
                    v-model="cyclicTopUp.isActive"
                    :label="$t('loyaltyCards_active')"
                    prepend-icon="mdi-tune"
                    name="isActive"
                  />
                </v-col>
              </v-row>
              <v-row v-show="isCyclicTopUp">
                <v-col
                  sm="12"
                  class="pt-0 pb-0"
                >
                  <v-textarea
                    v-model="cyclicTopUp.comment"
                    prepend-icon="mdi-comment-outline"
                    counter="60"
                    rows="3"
                    required
                    :label="$t('loyaltyCards_comment')"
                  />
                </v-col>
              </v-row>
            </v-form>
            <template
              v-if="clientId
                && clientId !== '0'
                && !isCyclicTopUp
                && canAccess('loyalty', 'clients')"
            >
              <v-row v-if="clientInvoiceBlocked">
                <v-col
                  cols="12"
                >
                  <v-alert
                    class="white--text my-0"
                    color="warning"
                    border="left"
                  >
                    {{ $t('loyaltyCards_clientLockWarning') }}
                  </v-alert>
                </v-col>
              </v-row>
              <v-row>
                <v-col
                  cols="6"
                >
                  <v-autocomplete
                    v-model="form.invoice.paymentTerm"
                    item-value="id"
                    :label="$t('common_paymentPeriod')"
                    :items="paymentTermOptions"
                    :disabled="clientInvoiceBlocked"
                    prepend-icon="mdi-calendar"
                  />
                </v-col>
                <v-col
                  cols="6"
                >
                  <v-select
                    v-model="form.invoice.paymentMethod"
                    item-value="id"
                    item-text="value"
                    :label="$t('common_paymentMethod')"
                    :items="paymentMethods"
                    :autocomplete="true"
                    :disabled="clientInvoiceBlocked"
                    prepend-icon="mdi-hand-coin-outline"
                    name="paymentMethod"
                    required
                  />
                </v-col>
              </v-row>
              <v-row
                class="mt-0"
              >
                <v-col
                  class="pt-0"
                >
                  <v-textarea
                    v-model="form.invoice.description"
                    :label="$t('common_description')"
                    name="description"
                    hide-details
                    rows="1"
                    auto-grow
                    prepend-icon="mdi-email-outline"
                  />
                </v-col>
              </v-row>
              <v-col class="pb-0">
                <span class="text-h6">{{ $t('loyaltyCards_confirmCustomerData') }}</span>
              </v-col>
              <v-expansion-panels flat>
                <v-expansion-panel>
                  <v-expansion-panel-header>
                    <v-row align="center">
                      <v-col>
                        <v-text-field
                          v-model="form.invoice.companyName"
                          :label="$t('common_fullCustomerName')"
                          disabled
                          hide-details
                          prepend-icon="mdi-rename-box"
                        />
                      </v-col>
                    </v-row>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-row>
                      <v-col>
                        <v-text-field
                          v-model="form.invoice.address"
                          :label="$t('common_formAddress')"
                          disabled
                          hide-details
                          prepend-icon="mdi-map-marker-outline"
                        />
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col>
                        <v-text-field
                          v-model="form.invoice.city"
                          :label="$t('common_city')"
                          disabled
                          hide-details
                          prepend-icon="mdi-city-variant-outline"
                        />
                      </v-col>
                      <v-col cols="4">
                        <v-text-field
                          v-model="form.invoice.postCode"
                          :label="$t('common_postCode')"
                          disabled
                          hide-details
                          prepend-icon="mdi-home-city-outline"
                        />
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col>
                        <v-text-field
                          v-model="form.invoice.taxNumber"
                          type="number"
                          :label="$t('table.tax_number')"
                          disabled
                          hide-details
                          prepend-icon="mdi-numeric"
                        />
                      </v-col>
                      <v-col cols="6">
                        <v-text-field
                          v-model="form.invoice.regon"
                          :label="$t('common_regonNumber')"
                          disabled
                          hide-details
                          prepend-icon="mdi-numeric"
                        />
                      </v-col>
                    </v-row>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
              <v-row align="center">
                <v-col class="pb-0">
                  <span class="text-h6">
                    {{ $t('loyaltyCards_emailInfo') }}
                  </span>
                </v-col>
              </v-row>
              <v-row align="center">
                <v-col>
                  <v-text-field
                    v-model="form.invoice.email"
                    :label="$t('common_email')"
                    disabled
                    hide-details
                    prepend-icon="mdi-email-outline"
                  />
                </v-col>
              </v-row>
            </template>
          </template>
        </v-card-text>
        <v-card-actions v-if="!loaders.site">
          <v-spacer />
          <v-btn
            color="gray"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            v-show="isCyclicTopUp"
            key="cyclic-submit-button"
            color="primary darken-1"
            :loading="loaders.cardRefill"
            :disabled="!buttons.cardRefill"
            @click.native="addCyclicTopUp()"
          >
            {{ $t('actions.refill') }}
          </v-btn>
          <v-btn
            v-show="!isCyclicTopUp"
            key="submit-topup-button"
            color="primary darken-1"
            :loading="loaders.cardRefill"
            :disabled="!buttons.cardRefill"
            @click.native="refillCard(false)"
          >
            {{ $t('actions.refill') }}
          </v-btn>
          <v-btn
            v-show="clientId && clientId !== '0' && !isCyclicTopUp"
            color="primary darken-1"
            :loading="loaders.cardRefill"
            :disabled="!buttons.cardRefill || card.toPay <= 0 || clientInvoiceBlocked"
            @click.native="refillCardAndGeneratInvoice"
          >
            {{ $t('actions.refill_and_issue_invoice') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import { format, subDays } from 'date-fns';
import DeviceTypeBadge from '@components/common/badge/DeviceTypeBadge.vue';
import ProgressBadge from '@components/common/badge/ProgressBadge.vue';
import TransactionTypeMixin from '@components/common/badge/icon-text-mixin/TransactionTypeMixin.vue';
import { mapGetters } from 'vuex';

export default {
  components: {
    ProgressBadge,
    DeviceTypeBadge,
    DateRangePicker,
  },
  mixins: [
    TransactionTypeMixin,
    SnackbarMixin,
  ],
  props: {
    cardNumber: {
      type: String,
      default: null,
    },
    cardToken: {
      type: String,
      default: null,
    },
    clientId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      paymentTermOptions: [
        {
          id: 'P0D',
          text: this.$t('common_0'),
        },
        {
          id: 'P1D',
          text: this.$t('common_1'),
        },
        {
          id: 'P3D',
          text: this.$t('common_3'),
        },
        {
          id: 'P5D',
          text: this.$t('common_5'),
        },
        {
          id: 'P7D',
          text: this.$t('common_7'),
        },
        {
          id: 'P10D',
          text: this.$t('common_10'),
        },
        {
          id: 'P14D',
          text: this.$t('common_14'),
        },
      ],
      currencySymbol: '!',
      loaders: {
        site: false,
        cardRefill: false,
      },
      buttons: {
        cardRefill: true,
      },
      topUpType: 'one_time',
      cyclicTopUp: {
        comment: '',
        type: '',
        dateFrom: format(subDays(new Date(), 7), 'YYYY-MM-DD'),
        dateTo: format(new Date(), 'YYYY-MM-DD'),
        isActive: true,
      },
      form: {
        invoice: {
          address: null,
          city: null,
          country: null,
          regon: null,
          companyName: null,
          postCode: null,
          invoiceStrategy: null,
          taxNumber: null,
          paymentTerm: null,
          paymentMethod: null,
          description: null,
        },
      },
      paymentMethods: [
        { id: 'cash', value: this.$t('common_cash') },
        { id: 'transfer', value: this.$t('common_transfer') },
      ],
      headers: [
        {
          value: 'cardNumber',
          text: this.$t('loyaltyCards_card'),
        },
        {
          value: 'cardAlias',
          text: this.$t('loyaltyCards_name'),
          align: 'end',
        },
        {
          value: 'client',
          text: this.$t('common_client'),
          align: 'end',
        },
        {
          value: 'comment',
          text: this.$t('common_comment'),
          align: 'end',
        },
        {
          value: 'isActive',
          text: this.$t('loyaltyCards_state'),
        },
        {
          value: 'startTime',
          text: this.$t('loyaltyCards_startTime'),
        },
        {
          value: 'endTime',
          text: this.$t('loyaltyCards_endTime'),
        },
        {
          value: 'type',
          text: this.$t('loyaltyCards_type'),
          align: 'end',
        },
        {
          value: 'lastCal',
          text: this.$t('loyaltyCards_lastCal'),
          align: 'center',
        },
        {
          value: 'value',
          text: this.$t('loyaltyCards_value'),
          align: 'end',
        },
        {
          value: 'actions',
          text: this.$t('actions.actions'),
          align: 'end',
        },
      ],
      notFullySentTopUpsList: [],
      cyclicTopUpsHistory: [],
      card: {
        token: this.cardToken,
        isActive: true,
        isDeleted: false,
        number: this.cardNumber,
        name: '',
        clientId: this.clientId,
        clientEmail: '',
        refillValue: 0,
        bonusValue: 0,
        discountPercent: 0,
        toPay: 0,
        toSend: 0,
      },
      isCardChanged: false,
      dialog: false,
      clients: [],
      clientsOptions: [],
      forms: {
        refill: {
          valid: true,
        },
        edit: {
          valid: true,
        },
        validationRules: {
          positiveNumber: (v) => Number(v) > 0 || this.$t(
            'loyaltyCards_topUpPositiveNumberOnly',
          ),
          nonNegativeNumber: (v) => Number(v) >= 0 || this.$t(
            'loyaltyCards_topUpPositiveNumberOnly',
          ),
          topupValue: (v) => Number(v) < 10000000000 || this.$t(
            'form.validation.top_up_value_to_big',
          ),
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      canAccess: 'auth/canAccess',
    }),
    amountLabel() {
      if (this.topUpType === 'cyclic_align') {
        return this.$t('transactions_refill_to_balance');
      }

      return this.$t('transactions.refill_for');
    },
    isCyclicTopUp() {
      if (this.topUpType === 'cyclic_add') {
        return true;
      }

      if (this.topUpType === 'cyclic_align') {
        return true;
      }

      return false;
    },
    lockAlertIcon() {
      return (this.card.isActive) ? 'lock_open' : 'lock';
    },
    lockAlertColor() {
      return (this.card.isActive) ? 'green' : 'red';
    },
    lockSwitchText() {
      return (this.card.isActive) ? this.$t('common_cardActive') : this.$t('common_cardBlocked');
    },
    clientInvoiceBlocked() {
      return this.form.invoice.invoiceStrategy === 'block';
    },
  },
  watch: {
    dialog(val) {
      if (val) {
        this.onModalShow();
      }
      if (val && this.card.clientId !== '0' && this.card.clientId !== null && this.card.clientId !== 0) {
        this.loaders.site = true;
        if (this.canAccess('loyalty', 'clients')) {
          this.getClientData();
        }
      }
      if (!val) {
        this.closeDialog();
      }
    },
    card: {
      handler() {
        this.isCardChanged = true;
      },
      deep: true,
    },
  },
  mounted() {
    this.clientsToMultiselectFormat();
  },
  methods: {
    onModalShow() {
      this.getCyclicTopUpHistoryList();
      this.getTopUpsNotSentList();
    },
    onDateRangeChange(dates) {
      this.cyclicTopUp.dateFrom = dates.from;
      this.cyclicTopUp.dateTo = dates.to;
    },
    maxDate() {
      const today = new Date();
      today.setFullYear(today.getFullYear() + 2);
      today.setMonth(11);
      today.setDate(31);

      return today.toISOString().slice(0, 10);
    },
    calculateBonus(type) {
      switch (type) {
        case 'value':
          this.card.bonusValue = ((this.card.discountPercent / 100)
          * this.card.refillValue).toFixed(2);
          this.card.toPay = (this.card.refillValue - this.card.bonusValue).toFixed(2);
          break;
        default:
          this.card.discountPercent = ((this.card.bonusValue
          / this.card.refillValue) * 100).toFixed(2);
          this.card.toPay = (this.card.refillValue - this.card.bonusValue).toFixed(2);
          break;
      }
    },
    clientsToMultiselectFormat() {
      this.clientsOptions = this.multiselectHelper.toSelect(this.clients);
      this.clientsOptions.unshift({
        id: 0,
        text: '',
      });
    },
    onError() {
      // on error
      this.loaders.cardClear = false;
      this.toggleButtons(true);
      this.closeDialog();
    },
    refillCardAndGeneratInvoice() {
      this.refillCard(true);
    },
    refillCard(generateInvoice) {
      if (this.$refs.formCardRefill.validate()) {
        this.disableButtonsExcept('cardRefill');
        this.loaders.cardRefill = true;

        this.axios.post(
          `/api/gateway/bkfpay-owner/card/${this.card.token}/top_ups`,
          {
            value: parseFloat(this.card.toPay),
            bonus: parseFloat(this.card.bonusValue),
            type: 'ADDITION',
            source: 'INTERNET',
            invoice: generateInvoice,
            description: this.form.invoice.description ?? null,
            paymentMethod: this.form.invoice.paymentMethod ?? null,
            paymentTerm: this.form.invoice.paymentTerm ?? null,
          },
        )
          .then(
            () => {
              this.loaders.cardRefill = false;
              this.toggleButtons(true);
              this.propagateCardUpdate();
              this.closeDialog();
            },
            (error) => {
              if (error.request && error.request.status === 423) {
                this.showSnackbar(
                  'warning',
                  this.$t('loyaltyCards_blockedTopup'),
                );
              }
              // on error
              this.toggleButtons(true);
              this.loaders.cardRefill = false;
            },
          );
      }
    },
    propagateCardUpdate() {
      this.$emit('reload-card-list');
    },
    getCyclicType() {
      if (this.topUpType === 'cyclic_add') {
        return 'ADD';
      }

      if (this.topUpType === 'cyclic_align') {
        return 'ALIGN';
      }

      return 'ADD';
    },
    addCyclicTopUp() {
      if (this.$refs.formCardRefill.validate()) {
        // this.disableButtonsExcept('cardRefill');
        // this.loaders.cardRefill = true;

        this.axios.post(
          `/api/gateway/bkfpay-owner/card/${this.card.token}/cyclic_top_up`,
          {
            value: parseFloat(this.card.refillValue),
            discount: parseFloat(this.card.discountPercent),
            comment: this.cyclicTopUp.comment,
            startTime: this.cyclicTopUp.dateFrom,
            endTime: this.cyclicTopUp.dateTo,
            isActive: this.cyclicTopUp.isActive ? 1 : 0,
            type: this.getCyclicType(),
          },
        )
          .then(
            () => {
              this.loaders.cardRefill = false;
              // this.toggleButtons(true);
              // this.propagateCardUpdate();
              this.closeDialog();
            },
            (error) => {
              if (error.request && error.request.status === 423) {
                this.showSnackbar(
                  'warning',
                  this.$t('loyaltyCards_blockedTopup'),
                );
              }
              // on error
              // this.toggleButtons(true);
              // this.loaders.cardRefill = false;
            },
          );
      }
    },
    closeDialog() {
      this.notFullySentTopUpsList = [];
      this.cyclicTopUpsHistory = [];
      this.cyclicTopUp = {
        comment: '',
        type: '',
        dateFrom: format(subDays(new Date(), 7), 'YYYY-MM-DD'),
        dateTo: format(new Date(), 'YYYY-MM-DD'),
        isActive: true,
      };
      this.card = {
        isActive: true,
        isDeleted: false,
        number: this.cardNumber,
        token: this.cardToken,
        name: '',
        clientId: this.clientId,
        clientEmail: '',
        refillValue: 0,
        bonusValue: 0,
        discountPercent: 0,
        toPay: 0,
        toSend: 0,
      };
      this.card.clientId = null;
      this.dialog = false;
    },
    toggleButtons(value) {
      this.buttons.cardRefill = value;
    },
    disableButtonsExcept(name) {
      this.toggleButtons(false);
      this.buttons[name] = true;
    },
    getTypeName(type) {
      if (type === 'ADD') {
        return this.$t('loyaltyCards_cyclicAdd');
      }

      if (type === 'ALIGN') {
        return this.$t('loyaltyCards_cyclicAlign');
      }

      return this.$t('loyaltyCards_cyclicAlign');
    },
    getTopUpsNotSentList() {
      // '&dateFrom=2023-11-01+00:00:00' +
      // '&dateTo=2023-11-30+23:59:59'

      this.loading = true;
      this.axios.get(
        '/api/loyalty/v3/top_ups',
        {
          params: {
            orderBy: 'createdAt',
            orderDescending: 1,
            status: 'NOT_FULLY_REFILLED,WAITING',
            isActive: true,
            cardToken: this.card.token,
            page: 1,
            perPage: 5,
          },
        },
      )
        .then(({ data }) => {
          this.notFullySentTopUpsList = data.data;
        })
        .finally(() => {
          this.loading = false;
          this.loaders.site = false;
        });
    },
    getCyclicTopUpHistoryList() {
      if (!this.canAccess('loyalty', 'cyclicTopUps')) {
        return;
      }
      this.loading = true;
      this.axios.get(
        '/api/loyalty/v3/cyclic_top_ups',
        {
          params: {
            isActive: true,
            cardToken: this.card.token,
            search: this.card.number,
            page: 1,
            limit: 5,
          },
        },
      )
        .then(({ data }) => {
          this.cyclicTopUpsHistory = data.data;
        })
        .finally(() => {
          this.loading = false;
          this.loaders.site = false;
        });
    },
    getClientData() {
      this.loading = true;
      this.axios.get(`/api/loyalty/v3/client/${this.clientId}`)
        .then(({ data }) => {
          this.form.invoice = {
            ...data,
            country: data.country,
          };
          this.card.discountPercent = data.discount ?? 0;
        })
        .finally(() => {
          this.loading = false;
          this.loaders.site = false;
        });
    },
  },
};
</script>
