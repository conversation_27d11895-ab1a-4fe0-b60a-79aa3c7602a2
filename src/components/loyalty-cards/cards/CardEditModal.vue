<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      scrollable
      persisten
      content-class="dialogWidth-3"
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('actions.edit') }} {{ card.number }}
            </h5>
          </span>
        </v-card-title>
        <v-progress-linear
          v-if="loaders.site"
          :indeterminate="true"
          class="mt-0"
        />
        <v-card-text class="pt-6">
          <div class="text-center">
            <v-progress-circular
              v-if="loaders.site"
              class="circleProgress"
              :size="90"
              :width="7"
              color="primary"
              indeterminate
            />
          </div>
          <template v-if="!loaders.site">
            <v-alert
              v-show="showRefillCancel"
              prominent
              dense
              text
              border="left"
              :value="true"
              type="warning"
              color="orange"
              icon="mdi-timer-sand-empty"
            >
              <v-form
                ref="formCardCancelRefill"
                v-model="forms.cancelRefill.valid"
                lazy-validation
              >
                <v-layout
                  wrap
                  align-center
                >
                  <v-col
                    sm="12"
                    md="8"
                  >
                    <strong>{{ $t('loyaltyCards_foundsWaitingForSent') }} {{
                      card.stats.toSend|currencySymbol(currencySymbol)
                    }} </strong>
                  </v-col>
                  <v-col
                    offset="5"
                    offset-sm="0"
                    xs="7"
                    sm="4"
                    md="4"
                  >
                    <v-btn
                      block
                      color="warning"
                      :loading="loaders.cardCancelRefill"
                      :disabled="!buttons.cardCancelRefill"
                      @click.native="cardCancelRefill"
                    >
                      {{ $t('actions.cancel_send') }}
                    </v-btn>
                  </v-col>
                </v-layout>
              </v-form>
            </v-alert>
            <v-alert
              prominent
              :value="true"
              dense
              border="left"
              text
              :color="lockAlertColor"
              :icon="lockAlertIcon"
            >
              <v-layout
                wrap
                align-center
              >
                <v-col
                  v-show="card.isActive"
                  sm="12"
                  md="7"
                  green--text
                  text--darken-2
                >
                  <strong>{{ $t('common_cardActive') }}.</strong>
                  {{ $t('loyaltyCards_activeMessage') }}
                </v-col>
                <v-col
                  v-show="!card.isActive"
                  sm="12"
                  md="7"
                  red--text
                  text--darken-2
                >
                  <strong>{{ $t('common_cardBlocked') }}.</strong>
                  {{ $t('loyaltyCards_blockedMessage') }}
                </v-col>
                <v-col
                  offset="5"
                  offset-sm="1"
                  offset-md="1"
                  xs="7"
                  sm="4"
                  md="4"
                  pt="20"
                >
                  <v-switch
                    v-model="card.isActive"
                    color="success"
                    :disabled="!buttons.cardToggle"
                    hide-details
                  >
                    <template #label>
                      <div class="pl-3 py-0">
                        {{ lockSwitchText }}
                      </div>
                    </template>
                  </v-switch>
                </v-col>
              </v-layout>
            </v-alert>
            <v-alert
              prominent
              dense
              text
              :value="true"
              border="left"
              type="error"
              color="red"
              icon="mdi-delete"
            >
              <v-layout
                wrap
                align-center
              >
                <v-col
                  xs="12"
                  sm="8"
                  md="8"
                >
                  <strong>{{ $t('loyaltyCards_removal') }}</strong> {{
                    $t('loyaltyCards_cleanHistoryAndBalance')
                  }}
                </v-col>
                <v-col
                  offset="5"
                  offset-sm="0"
                  xs="7"
                  sm="4"
                  md="4"
                >
                  <v-btn
                    block
                    color="error"
                    :disabled="!buttons.cardClear"
                    @click.native="dialogClearConfirm = true"
                  >
                    {{ $t('actions.delete') }}
                  </v-btn>
                </v-col>
              </v-layout>
            </v-alert>
            <v-container grid-list-md>
              <v-form
                ref="formCardEdit"
                v-model="forms.edit.valid"
                lazy-validation
              >
                <v-layout wrap>
                  <v-col
                    sm="12"
                    class="pt-2 pb-0"
                  >
                    <v-text-field
                      v-model="card.alias"
                      :label="$t('loyaltyCards_name')"
                      prepend-icon="mdi-format-title"
                    />
                  </v-col>
                  <v-col
                    sm="12"
                    class="pt-2 pb-0"
                    d-flex
                  >
                    <v-autocomplete
                      v-model="card.client.id"
                      item-value="id"
                      :search-input.sync="searchClientText"
                      :loading="clientIsLoading"
                      :label="$t('common_client')"
                      :items="clientsOptions"
                      :autocomplete="true"
                      :disabled="!canAccess('loyalty', 'clients')"
                      prepend-icon="mdi-account"
                    />
                  </v-col>
                  <v-col
                    sm="12"
                    class="pt-2 pb-0"
                  >
                    <v-text-field
                      v-model="card.email"
                      v-validate="'email|max:64|min:6'"
                      :label="$t('common_email')"
                      prepend-icon="mdi-email"
                      :data-vv-as="`${$t('common_email')}`"
                      :counter="64"
                      name="email"
                      :error-messages="errors.collect('email')"
                      required
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="pt-2 pb-0"
                  >
                    <v-textarea
                      v-model="card.description"
                      prepend-icon="mdi-comment"
                      :label="$t('admin_comment')"
                      class="descriptionText"
                      auto-grow
                      rows="1"
                    />
                  </v-col>
                </v-layout>
              </v-form>
            </v-container>
          </template>
        </v-card-text>
        <v-card-actions v-if="!loaders.site">
          <v-spacer />
          <v-btn
            color="gray"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loaders.actualize"
            @click.native="submit"
          >
            {{ $t('actions.update') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="dialogClearConfirm"
      content-class="dialogWidth-2"
      style="z-index: 1300"
      width="50%"
    >
      <v-card>
        <v-card-text class="pt-8 card-text-wrap">
          <div class="text-center">
            <v-icon class="p-6 red--text fa-4x">
              mdi-alert
            </v-icon>
            <p>{{ $t('loyaltyCards_hint') }}</p>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            text
            color="primary"
            :disabled="loaders.cardClear"
            @click.native="dialogClearConfirm = false"
          >
            {{ $t('actions.cancel') }}
          </v-btn>
          <v-btn
            color="red"
            class="white--text text-center"
            :loading="loaders.cardClear"
            @click.native="clearCard"
          >
            {{ $t('actions.delete') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>

import { mapGetters } from 'vuex';

export default {
  props: {
    cardNumber: {
      type: String,
      default: null,
    },
    cardToken: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loaders: {
        site: true,
        cardCancelRefill: false,
        cardLock: false,
        cardUnlock: false,
        cardClear: false,
        actualize: false,
      },
      buttons: {
        cardCancelRefill: true,
        cardToggle: true,
        cardClear: true,
      },
      searchClientText: null,
      clientIsLoading: false,
      clientInitialized: false,
      currencySymbol: null,
      card: {
        isActive: true,
        number: this.cardNumber,
        token: this.cardToken,
        alias: '',
        description: null,
        client: {
          id: null,
        },
        stats: {
          toSend: 0,
        },
        email: this.email,
      },
      isCardChanged: false,
      dialog: false,
      dialogClearConfirm: false,
      clientsOptions: [],
      forms: {
        cancelRefill: {
          valid: true,
        },
        edit: {
          valid: true,
        },
        validationRules: {
          positiveNumber: (v) => Number(v) >= 0 || this.$t(
            'loyaltyCards_topUpPositiveNumberOnly',
          ),
          email: [
            (v) => /^([a-zA-Z0-9_\-.+]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$|^$/.test(
              v,
            ) || this.$t('loyalApp_invalidValue'),
          ],
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      canAccess: 'auth/canAccess',
    }),
    lockAlertIcon() {
      return (this.card.isActive) ? 'mdi-lock-open' : 'mdi-lock';
    },
    lockAlertColor() {
      return (this.card.isActive) ? 'green' : 'red';
    },
    lockSwitchText() {
      return (this.card.isActive) ? this.$t('common_cardActive') : this.$t('common_cardBlocked');
    },
    showRefillCancel() {
      return this.card.stats.toSend;
    },
  },
  watch: {
    dialog() {
      // get card data only when dialog shows up
      if (this.dialog) {
        this.getData();
        if (this.card.client.id === 0) {
          this.fetchClientsOptions(null);
        }
      }
    },
    card: {
      handler() {
        this.isCardChanged = true;
      },
      deep: true,
    },
    searchClientText(val) {
      if (this.clientInitialized && val !== null) {
        this.fetchClientsOptions(val);
      }
      this.clientInitialized = true;
    },
  },
  methods: {
    async fetchClientsOptions(clientSearch) {
      if (!this.canAccess('loyalty', 'clients')) {
        return;
      }
      this.clientIsLoading = true;
      this.axios.get(
        '/api/loyalty/v3/clients',
        {
          params: {
            search: clientSearch,
            // page: 1,
            // perPage: 10,
          },
        },
      )
        .then(
          (response) => {
            this.clientsOptions = response.data.data.map((client) => ({
              id: client.id,
              text: client.companyName,
            }));
            this.clientIsLoading = false;
          },
          () => {
            this.clientIsLoading = false;
            this.onError();
          },
        );
    },
    async getData() {
      this.card.stats.toSend = 0;
      this.loaders.site = true;
      this.loaders.actualize = false;
      this.axios.get(
        `/api/loyalty/v3/card/${this.card.token}`,
      )
        .then(
          (response) => {
            this.card.alias = response.data.alias;
            this.card.number = response.data.number;
            this.card.description = response.data.description;
            this.card.isActive = response.data.status === 'ACTIVE';
            this.currencySymbol = response.data.currency.symbol ?? '';
            if (response.data.client != null) {
              this.clientsOptions = [
                {
                  id: response.data.client.id,
                  text: response.data.client.companyName,
                },
              ];
            }
            this.card.client.id = response.data.client ? response.data.client.id : 0;
            this.card.email = response.data.email;
            if (response.data.client && response.data.client.id === '') {
              this.card.client.id = 0;
            }
            this.card.stats.toSend = response.data.stats.toSend ?? 0;
            this.isCardChanged = false;
            this.loaders.site = false;
          },
          () => {
            this.onError();
          },
        );
    },
    clearCard() {
      this.disableButtonsExcept('cardClear');
      this.loaders.cardClear = true;
      this.axios.delete(
        `/api/loyalty/v3/card/${this.card.token}`,
      )
        .then(
          () => {
            this.loaders.cardClear = false;
            this.dialogClearConfirm = false;
            this.toggleButtons(true);
            this.closeDialog();
            this.propagateCardUpdate();
          },
          () => {
            this.onError();
          },
        );
    },
    onError() {
      // on error
      this.loaders.cardClear = false;
      this.dialogClearConfirm = false;
      this.toggleButtons(true);
      this.closeDialog();
    },
    cardCancelRefill() {
      this.loaders.cardCancelRefill = true;
      this.axios.delete(
        `/api/gateway/bkfpay-owner/card/${this.card.token}/top_ups`,
      )
        .then(
          () => {
            this.card.stats.toSend = 0;
            this.loaders.cardCancelRefill = false;
            this.closeDialog();
            this.propagateCardUpdate();
          },
          () => {
            // on error
            this.loaders.cardCancelRefill = false;
            this.loaders.site = false;
          },
        );
    },
    submit() {
      if (this.$refs.formCardEdit.validate()) {
        this.loaders.site = true;
        this.loaders.actualize = true;

        let clientId = this.card.client ? this.card.client.id : 0;
        if (clientId === 0) {
          clientId = null;
        }

        this.axios.patch(
          `/api/gateway/bkfpay-owner/card/${this.card.token}`,
          {
            status: this.card.isActive ? 'ACTIVE' : 'BLOCKED',
            email: this.card.email,
            alias: this.card.alias,
            description: this.card.description,
            clientId,
          },
        )
          .then(
            () => {
              if (this.isCardChanged) {
                this.propagateCardUpdate();
              }
              this.closeDialog();
            },
            () => {
              // on error
              this.loaders.site = false;
              this.loaders.actualize = false;
            },
          );
      }
    },
    propagateCardUpdate() {
      this.$emit('reload-card-list');
    },
    closeDialog() {
      this.dialog = false;
    },
    toggleButtons(value) {
      this.buttons.cardCancelRefill = value;
      this.buttons.cardToggle = value;
      this.buttons.cardClear = value;
    },
    disableButtonsExcept(name) {
      this.toggleButtons(false);
      this.buttons[name] = true;
    },
  },
};
</script>

<style scoped>
.card-text-wrap {
  display: flex;
  flex-direction: column;
}

.card-text-wrap p {
  font-size: 18px;
  text-align: center;
}
</style>
