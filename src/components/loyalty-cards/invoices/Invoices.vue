<template>
  <div>
    <v-layout
      row
      wrap
    >
      <v-col cols="12" />
      <v-col
        class="pt-0"
        cols="12"
      >
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="6"
            md="4"
          >
            <text-search
              v-model="filtering.search"
            />
          </v-col>
          <v-col
            sm="6"
            md="4"
            xs="12"
            offset-md="4"
          >
            <date-range-picker
              key="dateRange"
              ref="dateRange"
              prepend-icon="mdi-calendar-range"
              :show-presets="true"
              :show-custom="true"
              settings-namespace="loyalSystem:invoices:dates"
              start-preset="currentMonth"
              @reload-transaction-list="onDateRangeChangeCreated"
            />
          </v-col>
          <v-col
            cols="12"
            sm="8"
            class="text-sm-start"
          >
            <heading
              :dates="filtering.dates"
            />
          </v-col>
          <v-col
            cols="12"
            sm="4"
            class="d-flex justify-end"
          >
            <btn-refresh
              class="mr-2"
              @click="getData"
            />
            <report-create-modal
              btn-class="ml-2"
              :disabled="loader"
              :params="exportAsyncParams"
              :preset="filtering.dates.value"
            />
          </v-col>
        </v-layout>
      </v-col>
      <v-col
        cols="12"
        class="pt-0"
      >
        <invoices-table
          :loader="loader"
          :items="tableItems"
          :items-total="totalItems"
          :sums="sums"
          :options="filtering.options"
          @change="onFiltersChange"
        />
      </v-col>
    </v-layout>
  </div>
</template>

<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import formatDate from 'date-fns/format';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import Heading from './Heading.vue';
import InvoicesTable from './InvoicesTable.vue';

export default {
  name: 'InvoicesList',
  components: {
    ReportCreateModal,
    InvoicesTable,
    Heading,
    BtnRefresh,
    DateRangePicker,
    TextSearch,
  },
  mixins: [
    FiltersHandlingMixin,
    DataFetchMixin,
  ],
  data() {
    return {
      paymentDateFilter: false,
      dataUrl: '/api/loyalty/v3/invoices',
      filtering: {
        dateFrom: null,
        dateTo: null,
        search: null,
        options: {
          pagination: {
            page: 1,
            sortBy: ['date'],
            sortDesc: [true],
            itemsPerPage: 25,
          },
        },
      },
    };
  },
  computed: {
    exportAsyncParams() {
      return {
        report: 'v2\\LoyaltyInvoices3Report',
        startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
        endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
        search: this.filtering.search || null,
      };
    },
    tableItems() {
      if (this.loader) {
        return [];
      }

      return this.items;
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getExportFileName(name, fileType) {
      const today = formatDate(new Date(), 'YYYY-MM-DD');
      const nameArray = [[name, today].join('_'), fileType];

      return nameArray.join('.');
    },
    onDateRangeChangeCreated({
      from,
      to,
      value,
    }) {
      const dateFrom = new Date(from);
      const dateTo = new Date(to);
      dateTo.setDate(dateTo.getDate() + 1);

      this.filtering.dates.from = dateFrom.toISOString().replace('T', ' ');
      this.filtering.dates.to = dateTo.toISOString().replace('T', ' ');
      this.filtering.dates.value = value;
    },
    onFiltersChangePageReset(filters) {
      this.filtering.options.page = 1;
      this.onFiltersChange(filters);
    },
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows.map((row) => ({
        id: row.id,
        createdAt: row.createdAt,
        issuanceDate: row.issuanceDate,
        serviceDate: row.serviceDate,
        price: row.total,
        priceNet: row.totalNet,
        currency: row.currency,
        clientName: row.companyName,
        clientVatId: row.taxNumber,
        clientId: row.clientId,
        sendDate: row.sendDate,
        number: row.number,
      }));
    },
    getParams() {
      return {
        params: {
          sn: this.filtering.carwash || null,
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
          page: this.filtering.options.pagination.page || null,
          perPage: this.filtering.options.pagination.itemsPerPage || null,
          search: this.filtering.search || null,
        },
      };
    },
  },
};
</script>
