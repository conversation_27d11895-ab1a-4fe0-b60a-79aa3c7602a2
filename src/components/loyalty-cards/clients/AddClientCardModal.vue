<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      content-class="dialogWidth-3"
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">{{ $t('loyaltyCards_createNew') }}</h5>
          </span>
          <v-spacer />
          <v-btn
            icon
            text
            tile
            small
            dark
            @click="closeDialog"
          >
            <v-icon>
              mdi-close
            </v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text class="pt-6">
          <v-alert
            :value="true"
            border="left"
            type="info"
          >
            {{ $t('loyaltyCards_creationHint') }}
          </v-alert>

          <v-alert
            v-show="card.error"
            :value="true"
            type="error"
            border="left"
            :color="'red'"
            icon="mdi-lock"
          >
            {{ $t('loyaltyCards_deleteHint') }}
          </v-alert>

          <v-form
            ref="form"
            v-model="form.valid"
            :lazy-validation="true"
          >
            <v-container
              fluid
              class="px-0 py-0"
            >
              <v-row no-gutters>
                <v-col
                  class="hidden-xs-only pr-sm-6"
                  cols="12"
                  sm="7"
                  md="5"
                >
                  <img
                    alt="card-serial-number-placement"
                    style="height: 165px"
                    src="../../../assets/bl_card_sn_placement.png"
                  >
                </v-col>
                <v-col>
                  <v-checkbox
                    v-model="card.virtualCard"
                    :label="$t('loyaltyCards_virtualCard')"
                    prepend-icon="mdi-web"
                    name="sendSummaryReport"
                  />
                  <v-text-field
                    v-if="!card.virtualCard"
                    v-model="card.number"
                    prepend-icon="mdi-card-bulleted"
                    :label="$t('loyaltyCards_cardNumber')"
                    :rules="form.validationRules.cardNumber"
                    required
                    :validate-on-blur="form.validateOnBlur"
                  />
                  <v-text-field
                    v-model="card.credits"
                    prepend-icon="mdi-cash-plus"
                    :label="$t('loyaltyCards_topup') + ' ( ' + currencySymbol + ' )'"
                    :rules="form.validationRules.credits"
                    required
                    :validate-on-blur="form.validateOnBlur"
                  />
                  <v-text-field
                    v-model="card.name"
                    prepend-icon="mdi-format-title"
                    :label="$t('name')"
                  />
                  <v-text-field
                    v-model="card.email"
                    prepend-icon="mdi-email"
                    :label="$t('common_email')"
                  />
                  <small>*{{ $t('common_fieldRequired') }}</small>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary darken-1"
            :loading="loaders.submit"
            @click.native="submit"
          >
            {{ $t('actions.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>
import { mapGetters } from 'vuex';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  mixins: [
    SnackbarMixin,
  ],
  props: {
    clientId: {
      type: [Number],
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      card: {
        error: false,
        number: '',
        virtualCard: false,
        name: '',
        email: null,
        credits: '',
      },
      loaders: {
        submit: false,
      },
      form: {
        validateOnBlur: true,
        valid: true,
        validationRules: {
          cardNumber: [
            (v) => (v ? v.length === 8 : false)
                || this.$t('loyaltyCards_infoValidKeyLength'),
            (v) => /([abcdefABCDEF0-9]){8}$/.test(v)
                || this.$t('loyaltyCards_infoValidKey'),
          ],
          credits: [
            (v) => !v
                || Number(v) >= 0
                || this.$t('loyaltyCards_topUpPositiveNumberOnly'),
            (v) => Number(v) < 10000000000
                || this.$t('loyaltyCards_topUpValueToBig'),
          ],
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      currencySymbol: 'currency/getSymbol',
      canAccess: 'auth/canAccess',
    }),
  },

  methods: {
    show() {
      this.dialog = true;
    },
    resetForm() {
      this.card.number = '';
      this.$refs.form.reset();
    },
    submit() {
      this.form.validateOnBlur = false;
      if (this.$refs.form.validate()) {
        const data = {
          name: this.card.name ? this.card.name : '',
          clientId:
              this.clientId,
          email: this.card.email,
          value: this.card.credits,
        };

        if (!this.card.virtualCard) {
          data.number = this.card.number.toUpperCase();
        }

        this.loaders.submit = true;
        this.axios.post('/api/gateway/bkfpay-owner/cards', data)
          .then(
            (response) => {
              if (response.status === 201) {
                this.$emit('reload-card-list');
                this.resetForm();
                this.closeDialog();
                this.showSnackbar(
                  'success',
                  this.$t('common_success'),
                );
              }
            },
            (error) => {
              if (error.request && error.request.status === 409) {
                this.showSnackbar(
                  'warning',
                  this.$t('loyaltyCards_addExisting'),
                );
                // on error
                this.closeDialog();
              } else if (error.request && error.request.status === 400) {
                this.showSnackbar(
                  'warning',
                  this.$t('loyaltyCards_invalidData'),
                );
                // on error
                this.closeDialog();
              } else if (error.request && error.request.status === 406) {
                this.loaders.submit = false;
                this.card.error = true;
                this.showSnackbar(
                  'warning',
                  this.$t('loyaltyCards_invalidData'),
                );
              }
            },
          );
      }
    },
    closeDialog() {
      this.form.valid = true;
      this.loaders.submit = false;
      this.form.validateOnBlur = true;
      this.dialog = false;
      this.resetForm();
      this.card.error = false;
    },
  },
};
</script>
