<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        md="6"
        sm="6"
        cols="12"
      >
        <text-search
          v-model="search"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        sm="12"
        class="text-sm-start"
      >
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="8"
            class="text-sm-start"
          >
            <h2>
              <span>{{ $t('loyaltyCards_heading') }}</span>
            </h2>
          </v-col>
          <v-col
            cols="12"
            sm="4"
            class="d-flex justify-end"
          >
            <btn-refresh
              class="mr-2"
              @click="getData"
            />
            <v-btn
              class="ml-2"
              small
              color="primary"
              @click.stop
              @click.native="openModal('addClientDialog')"
            >
              <v-icon left>
                mdi-plus
              </v-icon>
              {{ $t('actions.add_client') }}
            </v-btn>
            <report-create-modal
              btn-class="ml-2"
              :show-dates="false"
              :params="exportListAsyncParams()"
              :disabled="loader"
            />
          </v-col>
        </v-layout>
      </v-col>
      <v-col
        cols="12"
        class="px-0 pt-0"
      >
        <v-data-table
          item-key="id"
          mobile-breakpoint="0"
          :headers="dataTable.headers"
          :items="dataTable.items"
          :loading="loader"
          :footer-props="footerProps"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
        >
          <template #progress>
            <div class="text-center mx-n4">
              <v-progress-linear
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>
          <template #item="{ item }">
            <template v-if="!loader">
              <tr>
                <td class="text-start">
                  {{ item.companyName }}
                  <v-tooltip
                    v-if="item.description !== null"
                    bottom
                  >
                    <template #activator="{ on }">
                      <v-icon
                        :key="item.id"
                        color="yellow darken-2"
                        v-on="on"
                      >
                        mdi-alert
                      </v-icon>
                    </template>
                    <span>{{ item.description }}</span>
                  </v-tooltip>
                </td>
                <td class="text-start hidden-sm-and-down">
                  {{ item.city }}
                </td>
                <td class="text-start hidden-sm-and-down">
                  {{ item.address }}
                </td>
                <td class="text-start">
                  {{ item.taxNumber }}
                </td>
                <td class="text-center">
                  {{ $t(`client-modal.invoice.strategies.${item.invoiceStrategy}`) }}
                </td>
                <td class="text-end d-flex justify-end align-center">
                  <div class="d-flex align-center">
                    <v-tooltip
                      v-if="item.alertLevel"
                      bottom
                    >
                      <template #activator="{ on, attrs }">
                        <v-icon
                          v-bind="attrs"
                          color="orange"
                          class="mr-2"
                          v-on="on"
                        >
                          mdi-alert-circle
                        </v-icon>
                      </template>
                      <span>{{ $t('client-modal.clientAlerts.editRequired') }}</span>
                    </v-tooltip>
                    <v-tooltip
                      bottom
                    >
                      <template #activator="{ on, attrs }">
                        <v-btn
                          x-small
                          tile
                          rounded
                          fab
                          class="ml-2"
                          color="secondary"
                          elevation="1"
                          v-bind="attrs"
                          v-on="on"
                          @click="$refs[`clientCardsListModal${item.id}`].show()"
                        >
                          <v-icon>mdi-credit-card-multiple</v-icon>
                        </v-btn>
                      </template>
                      <span>{{ $t('loyaltyCards_cardsList') }}</span>
                    </v-tooltip>
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-btn
                          x-small
                          tile
                          rounded
                          fab
                          class="ml-2"
                          color="secondary"
                          elevation="1"
                          v-bind="attrs"
                          v-on="on"
                          @click="$refs[`invoicesListDialog${item.id}`].show()"
                        >
                          <v-icon>mdi-file-document-outline</v-icon>
                        </v-btn>
                      </template>
                      <span>Lista faktur</span>
                    </v-tooltip>
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-btn
                          x-small
                          tile
                          rounded
                          fab
                          class="ml-2"
                          color="secondary"
                          elevation="1"
                          v-bind="attrs"
                          v-on="on"
                          @click="$refs[`clientCardTopUpsListModal${item.id}`].show()"
                        >
                          <v-icon>mdi-trending-up</v-icon>
                        </v-btn>
                      </template>
                      <span>{{ $t('common_topups') }}</span>
                    </v-tooltip>
                    <report-create-modal
                      :key="`modal_report_modal_client_${item.id}`"
                      btn-class="ml-2"
                      :params="exportAsyncParams(item)"
                      :disabled="loader"
                      :show-dates="true"
                      :tail-button="true"
                      color="primary darken-1"
                    />
                    <v-tooltip
                      :key="`clientListTooltipCLient${item.id}`"
                      bottom
                    >
                      <template #activator="{ on, attrs }">
                        <v-btn
                          :key="`editClientBtn${item.id}`"
                          x-small
                          tile
                          rounded
                          fab
                          class="ml-2"
                          color="primary"
                          elevation="1"
                          v-bind="attrs"
                          v-on="on"
                          @click="openModal(
                            `editClientDialog${item.id}`,
                            {clientNumber: item.id, email: item.email}
                          )"
                        >
                          <v-icon>mdi-pencil</v-icon>
                        </v-btn>
                      </template>
                      <span>{{ $t('loyaltyCards_editClient') }}</span>
                    </v-tooltip>
                  </div>
                </td>
              </tr>
              <client-add-edit-modal
                :key="`editClientDialog${item.id}`"
                :ref="`editClientDialog${item.id}`"
                :client-id="item.id"
                :email="item.email"
                action="edit"
                @on-client-update="getData()"
              />
              <client-cards-list-modal
                :key="`clientCardsListModal${item.id}`"
                :ref="`clientCardsListModal${item.id}`"
                :client-id="item.id"
                :client-name="item.companyName"
              />
              <client-card-top-ups-list-modal
                :key="`clientCardTopUpsListModal${item.id}`"
                :ref="`clientCardTopUpsListModal${item.id}`"
                :client-id="item.id"
                :client-name="item.companyName"
              />
              <client-invoices-list-modal
                :key="`invoicesListDialog${item.id}`"
                :ref="`invoicesListDialog${item.id}`"
                :client-id="item.id"
                :client-name="item.companyName"
              />
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <client-add-edit-modal
      key="addClientDialog"
      ref="addClientDialog"
      :user-number="null"
      action="add"
      @reload-client-list="getData"
    />
  </v-container>
</template>

<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import debounce from 'lodash/debounce';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import ClientAddEditModal from './ClientAddEditModal.vue';
import ClientCardsListModal from './ClientCardsListModal.vue';
import ClientCardTopUpsListModal from './ClientCardTopUpsListModal.vue';
import ClientInvoicesListModal from './ClientInvoicesListModal.vue';

export default {
  components: {
    ReportCreateModal,
    BtnRefresh,
    TextSearch,
    ClientAddEditModal,
    ClientCardsListModal,
    ClientCardTopUpsListModal,
    ClientInvoicesListModal,
  },
  data() {
    return {
      search: '',
      loader: false,
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['id'],
        sortDesc: [true],
      },
      footerProps: {
        'items-per-page-options': [10, 25, 50],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      clients: [],
      dataTable: {
        items: [],
        totalItems: -1,
        headers: [
          {
            text: this.$t('name'),
            value: 'companyName',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_city'),
            value: 'city',
            class: 'hidden-sm-and-down',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_formAddress'),
            value: 'address',
            class: 'hidden-sm-and-down',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('table.tax_number'),
            value: 'taxNumber',
            class: 'text-start',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyCards_invoicing'),
            value: 'invoiced_after_transaction',
            class: 'text-center',
            showInRowExpand: true,
            sortable: false,
            align: 'center',
          },
          {
            text: this.$t('actions.actions'),
            value: 'description',
            showInRowExpand: false,
            align: 'right',
            sortable: false,
          },
        ],
      },
      selectedClient: null,
    };
  },
  watch: {
    search: {
      handler() {
        this.pagination.page = 1;
        this.getDataDebounced();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if ((oldValue.sortDesc !== newValue.sortDesc
                || oldValue.page !== newValue.page
                || oldValue.itemsPerPage !== newValue.itemsPerPage
                || oldValue.sortBy !== newValue.sortBy)
            && this.autoLoadData) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
              || oldValue.sortBy !== newValue.sortBy
              || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          this.getDataDebounced();
        }
      },
      deep: true,
    },
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.getData();
    }, 1000);
  },
  mounted() {
    this.getData();
  },
  methods: {
    exportListAsyncParams() {
      return {
        report: 'v2\\LoyaltyClients3Report',
      };
    },
    exportAsyncParams(client) {
      return {
        report: 'v2\\LoyaltyClientUsageReport',
        clientId: client.id,
        // order: this.pagination.sortBy[0],
        // desc: this.pagination.sortDesc[0],
        // search: this.filtering.search.text,
        // status: this.filtering.status,
        // foundsExists: this.filtering.foundsExists,
        // nameExists: this.filtering.nameExists,
        // isVirtual: this.filtering.virtual,
      };
    },
    getData() {
      this.autoLoadData = true;
      this.loader = true;
      this.axios.get(
        '/api/loyalty/v3/clients',
        {
          params: {
            search: this.search,
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
          },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.clients = response.data.data;
            this.dataTable.items = response.data.data;
            this.dataTable.totalItems = Number(response.data.total);
            this.loader = false;
          }
        })
        .catch((error) => {
          if (error.response && error.response.status === 402) {
            this.loader = false;
          }
        });
    },
    openModal(modal, item) {
      if (this.$refs[modal]) {
        // Ensure clean state before opening
        if (Array.isArray(this.$refs[modal])) {
          this.$refs[modal][0].dialog = true;
        } else {
          this.$refs[modal].dialog = true;
        }
        if (item) {
          this.selectedClient = item;
        }
      }
    },
  },
};
</script>
