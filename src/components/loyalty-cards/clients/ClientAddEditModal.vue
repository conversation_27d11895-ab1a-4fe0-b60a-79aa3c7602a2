<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      scrollable
      content-class="dialogWidth-3"
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              <template v-if="modalAction == 'edit'">
                {{ $t('loyaltyCards_editClient') }} {{ initiationEmail }}
              </template>
              <template v-else>
                {{ $t('actions.add_client') }}
              </template>
            </h5>
          </span>
        </v-card-title>
        <v-progress-linear
          v-if="loaders.site"
          :indeterminate="true"
          class="mt-0"
        />
        <v-card-text class="pt-6">
          <div class="text-center">
            <v-progress-circular
              v-if="loaders.site"
              class="circleProgress"
              :size="90"
              :width="7"
              color="primary"
              indeterminate
            />
          </div>
          <template v-if="!loaders.site">
            <v-container grid-list-md>
              <v-alert
                v-for="(alert, index) in client.alerts"
                :key="index"
                :type="alert.level"
                :value="true"
                class="mb-4"
              >
                <div class="text-h6 white--text font-weight-bold">
                  {{ alert.title }}
                </div>
                <div class="white--text">
                  {{ alert.text }}
                </div>
              </v-alert>

              <v-form
                ref="formClientEdit"
                v-model="form.valid"
                lazy-validation
              >
                <v-layout wrap>
                  <v-col
                    cols="12"
                    class="pt-0 pb-0"
                  >
                    <span class="text-h6">{{ $t('common_clientBasicData') }}</span>
                  </v-col>
                  <v-col
                    cols="6"
                    class="pt-0 pb-0"
                  >
                    <v-text-field
                      v-model="client.email"
                      v-validate="'email|max:64|min:6'"
                      :label="$t('common_email')"
                      prepend-icon="mdi-email"
                      :data-vv-as="`${$t('common_email')}`"
                      :counter="64"
                      name="email"
                      :error-messages="errors.collect('email')"
                      required
                    />
                  </v-col>
                  <v-col
                    cols="6"
                    class="pt-0 pb-0"
                  >
                    <v-text-field
                      v-model="client.phone"
                      :label="$t('common_phone')"
                      prepend-icon="mdi-phone"
                      name="phone"
                      :data-vv-as="`${$t('common_phone')}`"
                      :error-messages="errors.collect('phone')"
                    />
                  </v-col>
                  <v-col
                    cols="6"
                    class="pt-0 pb-0"
                  >
                    <v-text-field
                      v-model="client.firstname"
                      v-validate="'alpha_spaces|max:32'"
                      :label="$t('common_firstName')"
                      prepend-icon="mdi-account"
                      name="firstName"
                      :data-vv-as="`${$t('common_firstName')}`"
                      :error-messages="errors.collect('firstName')"
                    />
                  </v-col>
                  <v-col
                    cols="6"
                    class="pt-0 pb-0"
                  >
                    <v-text-field
                      v-model="client.lastname"
                      v-validate="'alpha_spaces|max:64'"
                      :label="$t('common_lastName')"
                      prepend-icon="mdi-account"
                      name="lastName"
                      :data-vv-as="`${$t('common_lastName')}`"
                      :error-messages="errors.collect('lastName')"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="pt-2 pb-0"
                  >
                    <v-textarea
                      v-model="client.description"
                      prepend-icon="mdi-comment"
                      :label="$t('admin_comment')"
                      class="descriptionText"
                      auto-grow
                      rows="1"
                    />
                  </v-col>
                  <v-row align="center">
                    <v-col
                      cols="12"
                      class="pb-0"
                    >
                      <span
                        class="text-h6"
                      >
                        {{ $t('loyaltyCards_configuration') }}
                      </span>
                    </v-col>
                  </v-row>
                  <v-col
                    cols="12"
                    class="py-0"
                  />
                  <v-col
                    cols="12"
                    class="pt-4 pb-0"
                  >
                    <v-select
                      v-model="client.invoiceStrategy"
                      v-validate="'required'"
                      item-value="id"
                      item-text="value"
                      :label="$t('loyaltyCards_generateStrategy')"
                      :items="strategyOptions"
                      :autocomplete="true"
                      :rules="rules.selectRequired2"
                      prepend-icon="mdi-strategy"
                      name="invoiceStrategy"
                      @change="resetAndValidate"
                    />
                  </v-col>
                  <v-col
                    cols="4"
                    class="pt-0 pb-0"
                  >
                    <v-text-field
                      v-model="client.discount"
                      v-validate="'required|min_value:0|max_value:100'"
                      :label="$t('common_discount')"
                      prepend-icon="mdi-percent-outline"
                      name="discount"
                      :data-vv-as="`${$t('common_discount')}`"
                      :error-messages="errors.collect('discount')"
                    />
                  </v-col>
                  <v-col
                    cols="4"
                    class="pt-0 pb-0"
                  >
                    <v-select
                      v-model="client.paymentMethod"
                      v-validate="'required|min:1'"
                      item-value="id"
                      item-text="value"
                      :label="$t('common_paymentMethod')"
                      :items="paymentMethods"
                      :rules="rules.selectRequired2"
                      prepend-icon="mdi-hand-coin-outline"
                      name="paymentMethod"
                      @change="resetAndValidate"
                    />
                  </v-col>
                  <v-col
                    cols="4"
                    class="pt-0 pb-0"
                  >
                    <v-autocomplete
                      v-model="client.paymentTerm"
                      v-validate="'required|min:1'"
                      item-value="id"
                      :label="$t('common_paymentPeriod')"
                      :items="paymentTermOptions"
                      prepend-icon="mdi-update"
                      name="paymentPeriodType"
                      :data-vv-as="`${$t('common_paymentPeriod')}`"
                      :error-messages="errors.collect('paymentPeriod')"
                      :rules="rules.selectRequired2"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="pt-0 pb-0"
                  >
                    <v-checkbox
                      v-model="client.sendSummaryReport"
                      :label="$t('loyaltyCards_clientCardSummaryReport')"
                      prepend-icon="mdi-chart-bar"
                      name="sendSummaryReport"
                    />
                  </v-col>
                  <v-row align="center">
                    <v-col class="pb-0">
                      <span class="text-h6">{{ $t('common_clientInvoiceData') }}</span>
                    </v-col>
                  </v-row>
                  <v-col
                    cols="12"
                    class="pt-4 pb-0"
                  >
                    <v-text-field
                      v-model="client.companyName"
                      v-validate="'required|max:128|min:1'"
                      :label="$t('common_fullCustomerName')"
                      prepend-icon="mdi-rename-box"
                      name="companyName"
                      :data-vv-as="`${$t('common_fullCustomerName')}`"
                      :error-messages="errors.collect('companyName')"
                      :rules="rules.required"
                      required
                    />
                  </v-col>
                  <v-col
                    cols="6"
                    class="pt-0 pb-0"
                  >
                    <v-text-field
                      v-model="client.taxNumber"
                      v-validate="'required|alpha_num|max:24'"
                      :label="$t('table.tax_number')"
                      prepend-icon="mdi-numeric"
                      name="taxNumber"
                      :data-vv-as="`${$t('table.tax_number')}`"
                      :error-messages="errors.collect('taxNumber')"
                      :rules="rules.selectRequired"
                      required
                    />
                  </v-col>
                  <v-col
                    cols="6"
                    class="pt-0 pb-0"
                  >
                    <v-text-field
                      v-model="client.regon"
                      v-validate="'alpha_num|max:24'"
                      :label="$t('common_regonNumber')"
                      prepend-icon="mdi-numeric"
                      name="regon"
                      :data-vv-as="`${$t('common_regonNumber')}`"
                      :error-messages="errors.collect('regon')"
                    />
                  </v-col>
                  <v-col
                    cols="8"
                    class="pt-0 pb-0"
                  >
                    <v-text-field
                      v-model="client.city"
                      v-validate="'max:128'"
                      :label="$t('common_city')"
                      prepend-icon="mdi-city-variant-outline"
                      name="city"
                      :data-vv-as="`${$t('common_city')}`"
                      :error-messages="errors.collect('city')"
                      :required="clientDataValidation"
                      :rules="rules.selectRequired"
                    />
                  </v-col>
                  <v-col
                    cols="4"
                    class="pt-0 pb-0"
                  >
                    <v-text-field
                      v-model="client.postCode"
                      v-validate="'max:12'"
                      :label="$t('common_postCode')"
                      prepend-icon="mdi-home-city-outline"
                      name="postCode"
                      :data-vv-as="`${$t('common_postCode')}`"
                      :error-messages="errors.collect('postCode')"
                      :required="clientDataValidation"
                      :rules="rules.selectRequired"
                    />
                  </v-col>
                  <v-col
                    cols="7"
                    class="pt-0 pb-0"
                  >
                    <v-text-field
                      v-model="client.address"
                      v-validate="'max:255'"
                      :label="$t('common_formAddress')"
                      prepend-icon="mdi-map-marker-outline"
                      name="address"
                      :data-vv-as="`${$t('common_formAddress')}`"
                      :error-messages="errors.collect('address')"
                      :required="clientDataValidation"
                      :rules="rules.selectRequired"
                    />
                  </v-col>
                  <v-col
                    cols="5"
                    class="pt-0 pb-0"
                  >
                    <v-autocomplete
                      v-model="client.country"
                      item-value="shortName"
                      item-text="name"
                      :label="$t('admin_country')"
                      :items="countriesOptions"
                      :autocomplete="true"
                      clearable
                      prepend-icon="mdi-earth"
                    />
                  </v-col>
                </v-layout>
              </v-form>
            </v-container>
          </template>
        </v-card-text>
        <v-card-actions v-if="!loaders.site">
          <v-spacer />
          <v-btn
            color="gray"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loaders.actualize"
            :disabled="!form.valid || clientExists"
            @click.native="submit"
          >
            <template v-if="modalAction == 'edit'">
              {{ $t('actions.save') }}
            </template>
            <template v-else>
              {{ $t('actions.add_client') }}
            </template>
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>
export default {
  props: {
    clientId: {
      type: Number,
      default: null,
    },
    action: {
      type: String,
      default: 'add',
    },
    email: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      paymentTermOptions: [
        {
          id: 'P0D',
          text: this.$t('common_0'),
        },
        {
          id: 'P1D',
          text: this.$t('common_1'),
        },
        {
          id: 'P3D',
          text: this.$t('common_3'),
        },
        {
          id: 'P5D',
          text: this.$t('common_5'),
        },
        {
          id: 'P7D',
          text: this.$t('common_7'),
        },
        {
          id: 'P10D',
          text: this.$t('common_10'),
        },
        {
          id: 'P14D',
          text: this.$t('common_14'),
        },
      ],
      initiationEmail: this.email,
      currencies: [],
      countriesOptions: [],
      clientExists: false,
      modalAction: this.action,
      loaders: {
        site: true,
        actualize: false,
      },
      client: {
        sendSummaryReport: true,
        invoiceStrategy: 'block',
        companyName: null,
        email: null,
        lastname: null,
        firstname: null,
        phone: null,
        address: null,
        country: null,
        taxNumber: null,
        regon: null,
        postCode: null,
        city: null,
        discount: 0,
        description: null,
        paymentMethod: 'cash',
        paymentTerm: 'P0D',
      },
      dialog: false,
      strategyOptions: [
        { id: 'block', value: this.$t('client-modal.invoice.strategies.block') },
        { id: 'manual', value: this.$t('client-modal.invoice.strategies.manual') },
        { id: 'auto-after-top-up', value: this.$t('client-modal.invoice.strategies.auto-after-top-up') },
        { id: 'aggregated_month', value: this.$t('client-modal.invoice.strategies.aggregated_month') },
      ],
      paymentMethods: [
        { id: 'cash', value: this.$t('common_cash') },
        { id: 'transfer', value: this.$t('common_transfer') },
        { id: 'online', value: this.$t('common_online') },
      ],
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      rules: {
        required: [(v) => !!v || this.$t('common_fieldRequired')],
        selectRequired: [(v) => (!!v || !this.clientDataValidation) || this.$t('common_fieldRequired')],
        selectRequired2: [(v) => (v !== null && v.length > 0) || this.$t('common_fieldRequired')],
        phone: [(v) => /(^\+?[0-9]{8,14}$)|(^$)/u.test(v) || this.$t('loyaltyCards_phoneNumber')],
      },
    };
  },
  computed: {
    clientDataValidation() {
      return false;
      // return this.client.invoiceStrategy !== 'block';
    },
  },
  watch: {
    dialog(val) {
      if (val) {
        this.getCountries();
        // Reset form and validation state before showing modal
        this.$nextTick(() => {
          this.resetValidationErrorsAndClearFields();
          this.loaders.actualize = false;
          this.loaders.site = false;

          if (this.modalAction === 'edit') {
            this.getData();
          }
        });
      } else {
        // Clean up when modal is closed
        this.resetValidationErrorsAndClearFields();
      }
    },
  },
  beforeDestroy() {
    this.resetValidationErrorsAndClearFields();
  },
  methods: {
    getCountries() {
      this.axios.get(
        '/api/lists/countries',
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.countriesOptions = response.data;
          }
        });
    },
    validateAll() {
      return this.$validator.validateAll();
    },
    resetAndValidate() {
      this.$validator.reset();
      return this.validateAll();
    },
    resetValidationErrorsAndClearFields() {
      this.clearFormData();
      if (this.$refs.formClientEdit) {
        this.$refs.formClientEdit.reset();
        this.$refs.formClientEdit.resetValidation();
      }
      this.$validator.reset();
      this.$nextTick(() => {
        if (this.$refs.formClientEdit) {
          this.$refs.formClientEdit.validate();
        }
      });
    },
    getData() {
      this.loaders.site = true;
      this.loaders.actualize = false;
      this.axios.get(
        `/api/gateway/bkfpay-owner/client/${this.clientId}`,
      )
        .then(
          (response) => {
            this.client.sendSummaryReport = response.data.sendSummaryReport;
            this.client.email = response.data.email;
            this.client.firstname = response.data.firstname;
            this.client.lastname = response.data.lastname;
            this.client.phone = response.data.phone;
            this.client.city = response.data.city;
            this.client.address = response.data.address;
            this.client.country = response.data.country;
            this.client.postCode = response.data.postCode;
            this.client.companyName = response.data.companyName;
            this.client.taxNumber = response.data.taxNumber;
            this.client.description = response.data.description;
            this.client.regon = response.data.regon;
            this.client.invoiceStrategy = response.data.invoiceStrategy;
            this.client.discount = response.data.discount;
            this.client.paymentTerm = response.data.paymentTerm;
            this.client.paymentMethod = response.data.paymentMethod;
            this.client.alerts = response.data.alerts;

            this.loaders.site = false;
          },
          () => {
            this.onError();
          },
        );
    },
    onError() {
      // on error
      this.closeDialog();
    },
    editClient() {
      this.$validator.validateAll();
      if (this.$refs.formClientEdit.validate()) {
        this.loaders.site = true;
        this.loaders.actualize = true;

        const parameters = JSON.parse(JSON.stringify(this.client));
        parameters.discount = parseInt(this.client.discount, 10);

        const url = `/api/gateway/bkfpay-owner/client/${this.clientId}`;

        this.axios.patch(
          url,
          parameters,
        )
          .then(
            () => {
              this.propagateClientUpdate();
              this.closeDialog();
            },
            () => {
              // on error
              this.loaders.site = false;
              this.loaders.actualize = false;
            },
          );
      }
    },
    addClient() {
      this.$validator.validateAll();
      if (this.$refs.formClientEdit.validate()) {
        this.loaders.site = true;
        this.loaders.actualize = true;

        const parameters = {
          ...this.client,
        };

        parameters.discount = parseInt(this.client.discount, 10);

        this.axios.post(
          '/api/gateway/bkfpay-owner/clients',
          parameters,
        )
          .then(
            () => {
              this.propagateClientUpdate();
              this.closeDialog();
            },
            () => {
              // on error
              this.loaders.site = false;
              this.loaders.actualize = false;
            },
          );
      }
    },
    submit() {
      if (this.modalAction === 'add') {
        this.addClient();
      } else {
        this.editClient();
      }
    },
    clearFormData() {
      this.client = {
        sendSummaryReport: true,
        invoiceStrategy: 'block',
        companyName: null,
        email: null,
        lastname: null,
        firstname: null,
        phone: null,
        address: null,
        taxNumber: null,
        regon: null,
        country: null,
        postCode: null,
        city: null,
        discount: 0,
        paymentMethod: 'cash',
        paymentTerm: 'P0D',
      };
    },
    propagateClientUpdate() {
      this.$emit('on-client-update');
    },
    closeDialog() {
      this.dialog = false;
      this.$nextTick(() => {
        this.resetValidationErrorsAndClearFields();
      });
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
  },
};
</script>

<style scoped>
.card-text-wrap {
  display: flex;
  flex-direction: column;
}

.card-text-wrap p {
  font-size: 18px;
  text-align: center;
}
</style>
