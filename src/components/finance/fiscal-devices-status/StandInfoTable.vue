<template>
  <v-container
    fluid
    class="pa-0"
  >
    <v-card
      flat
      outlined
      color="blue-grey lighten-5"
      class="pa-3"
    >
      <i
        v-if="!standsInfo"
        class="ml-2"
      >{{ $t('common_noData') }}</i>
      <div v-else>
        <v-row
          v-for="stand in stands"
          :key="stand.bayId"
          class="ma-0 mb-2"
        >
          <v-col class="pa-1">
            <v-card
              outlined
              elevation="2"
            >
              <v-card-title class="py-2">
                <device-type-badge
                  :device-type="stand.source"
                  :stand-id="stand.bayId"
                />
              </v-card-title>
              <v-divider />
              <v-simple-table
                dense
              >
                <template #default>
                  <tbody>
                    <tr
                      v-for="[key, value]
                        in Object.entries(stand)
                          .filter
                          (([key, value]) => key !== 'source' && key !== 'bayId' && value !== null)"
                      :key="key"
                      class="striped-row"
                    >
                      <td class="py-1 px-3">
                        {{ key }}
                      </td>
                      <td class="py-1 px-3">
                        {{ value }}
                      </td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </v-card>
  </v-container>
</template>

<script>

import DeviceTypeBadge from '@/components/common/badge/DeviceTypeBadge.vue';

export default {
  name: 'StandInfoTable',

  components: {
    DeviceTypeBadge,
  },

  props: {
    standsInfo: {
      type: [Array, null],
      required: false,
      default: null,
    },
  },

  computed: {
    stands() {
      return this.standsInfo.slice().sort((a, b) => a.bayId - b.bayId);
    },
  },
};
</script>
