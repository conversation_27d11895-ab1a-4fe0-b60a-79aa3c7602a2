<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      content-class="dialogWidth-3"
      persistent
      :fullscreen="$vuetify.breakpoint.xsOnly"
    >
      <v-card :loading="loading">
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('fiscal_transactions_certificate_uploadBtn') }}
            </h5>
          </span>
          <v-spacer />
          <v-btn
            icon
            text
            tile
            small
            color="white"
            @click="closeDialog"
          >
            <v-icon color="white">
              mdi-close
            </v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text class="pt-6">
          <v-alert
            v-if="errorMessage"
            border="left"
            type="error"
            dense
            class="mb-4"
          >
            {{ errorMessage }}
          </v-alert>
          <v-container grid-list-md>
            <v-form
              ref="form"
              v-model="valid"
              lazy-validation
            >
              <v-row>
                <v-col cols="12">
                  <v-text-field
                    v-model="oib"
                    prepend-icon="mdi-identifier"
                    :label="$t('fiscal_transactions_config_oib')"
                    :rules="oibRules"
                    required
                  />
                  <div class="d-flex align-center">
                    <v-text-field
                      :value="certificateFileName"
                      prepend-icon="mdi-certificate"
                      :label="$t('fiscal_transactions_certificate_file')"
                      readonly
                      class="mr-4"
                      :rules="fileRules"
                      required
                    />
                    <input
                      ref="fileInput"
                      type="file"
                      accept=".p12"
                      style="display: none"
                      @change="onFileSelected"
                    >
                    <v-btn
                      color="primary"
                      @click="$refs.fileInput.click()"
                    >
                      {{ $t('common_select_file') }}
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary"
            text
            :disabled="loading"
            @click="closeDialog"
          >
            {{ $t('common_cancel') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loading"
            :disabled="!valid || loading"
            @click="uploadCertificate"
          >
            {{ $t('common_upload') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  name: 'UploadCertificateModal',
  mixins: [
    SnackbarMixin,
  ],
  props: {
    deviceSn: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      valid: false,
      loading: false,
      oib: '',
      certificateFile: null,
      certificateFileName: '',
      errorMessage: '',
      oibRules: [
        (v) => !!v || this.$t('common_fieldRequired'),
        (v) => /^\d{11}$/.test(v) || this.$t('fiscal_transactions_certificate_oibFormat'),
      ],
      fileRules: [
        () => !!this.certificateFile || this.$t('common_fieldRequired'),
        () => !this.certificateFile || this.certificateFile.name.endsWith('.p12') || this.$t('fiscal_transactions_certificate_fileFormat'),
      ],
    };
  },
  methods: {
    openDialog() {
      this.resetForm();
      this.dialog = true;
    },
    closeDialog() {
      this.dialog = false;
      this.resetForm();
    },
    resetForm() {
      this.oib = '';
      this.certificateFile = null;
      this.certificateFileName = '';
      this.errorMessage = '';
      this.loading = false;
      if (this.$refs.form) {
        this.$refs.form.resetValidation();
      }
    },
    onFileSelected(event) {
      const { files } = event.target;
      if (files.length > 0) {
        [this.certificateFile] = files;
        this.certificateFileName = this.certificateFile.name;
      } else {
        this.certificateFile = null;
        this.certificateFileName = '';
      }
      this.$refs.form.validate();
    },
    async uploadCertificate() {
      if (!this.$refs.form.validate()) {
        return;
      }

      this.loading = true;
      this.errorMessage = '';

      try {
        const formData = new FormData();
        formData.append('certificate', this.certificateFile);
        formData.append('oib', this.oib);

        const response = await this.axios.post(
          `/api/finance/fiscal/certificate/${this.deviceSn}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );

        if (response.data.success) {
          this.showSnackbar('success', this.$t('common_success'));
          this.closeDialog();
          this.$emit('certificate-uploaded');
        } else {
          this.errorMessage = response.data.error || this.$t('fiscal_transactions_certificate_error');
        }
      } catch (error) {
        this.errorMessage = error.response?.data?.error || this.$t('fiscal_transactions_certificate_error');
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
