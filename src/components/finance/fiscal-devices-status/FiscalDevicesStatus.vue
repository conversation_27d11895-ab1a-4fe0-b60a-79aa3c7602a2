<template>
  <v-row>
    <v-col
      cols="12"
      class="mt-5"
    >
      <v-layout
        row
        wrap
      >
        <v-col
          cols="12"
          sm="8"
          class="text-sm-start"
        >
          <h2>
            <span>{{ $t('fiscal_transactions.fiscalDevicesDetails') }}</span>
          </h2>
        </v-col>
        <v-col
          cols="12"
          sm="4"
          class="d-flex justify-end"
        >
          <btn-refresh
            class="ml-2"
            :disabled="loader"
            @click="refreshData"
          />
          <report-create-modal
            btn-class="ml-2"
            :params="exportAsyncParams"
            :disabled="loader"
            :show-dates="false"
          />
        </v-col>
      </v-layout>
    </v-col>
    <v-col
      cols="12"
      class="pt-0"
    >
      <fiscal-devices-status-table
        :loader="loader"
        :items="items"
        :show-fiscal-config="showFiscalConfig"
        :fiscal-config="fiscalConfig"
        :options="filtering.options"
        @change="onFiltersChange"
        @refresh-data="refreshData"
      />
    </v-col>
  </v-row>
</template>

<script>
import { mapGetters } from 'vuex';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import FiscalDevicesStatusTable from '@components/finance/fiscal-devices-status/FiscalDevicesStatusTable.vue';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import DateOptionsMixins from '@components/common/mixins/DateOptionsMixins.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  name: 'FiscalDevicesStatus',
  components: {
    ReportCreateModal,
    FiscalDevicesStatusTable,
    BtnRefresh,
  },
  mixins: [
    DateOptionsMixins,
    SettingsMixin,
    DataFetchMixin,
    FilterMixin,
    FiltersHandlingMixin,
    SnackbarMixin,
  ],
  props: {
    carwash: {
      type: [Object, Number],
      default: null,
      nullable: true,
    },
  },
  data() {
    return {
      dataUrl: '/api/reports/data',
      fiscalConfig: {},
    };
  },
  computed: {
    ...mapGetters({
      userCountry: 'auth/userCountry',
    }),
    exportAsyncParams() {
      const carwash = this.filtering.carwash || null;
      return {
        serial: carwash,
        report: 'v2\\FinanceFiscalDevicesStatus',
      };
    },
    showFiscalConfig() {
      return this.userCountry === 'HR';
    },
  },
  watch: {
    carwash(val) {
      this.$set(this.filtering, 'carwash', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
  },
  mounted() {
    if (this.dates !== null) {
      this.refreshData();
    }
  },
  methods: {
    onFiltersChangePageReset(filters) {
      this.onFiltersChange(filters);
      // debouncing and close all lists
    },
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows.map((row, index) => ({
        ...row,
        id: index,
        carwashName: row.carwashName,
        dates: this.filtering.dates,
      }));
    },
    getParams() {
      return {
        params: {
          serial: this.filtering.carwash || null,
          report: 'v2\\FinanceFiscalDevicesStatus',
        },
      };
    },
    refreshData() {
      this.getData();
      if (this.showFiscalConfig) {
        this.getFiscalConfigData();
      }
    },
    getFiscalConfigData() {
      this.axios.get('/api/finance/fiscal/config')
        .then((response) => {
          if (response.status === 200 && response.data) {
            const configData = {};
            response.data.forEach((item) => {
              configData[item.sn] = item.config;
            });
            this.fiscalConfig = configData;
          }
        })
        .catch(() => {
          this.showSnackbar('error', this.$t('fiscal_transactions_config_error'));
        });
    },
  },
};
</script>
