<template>
  <v-form
    ref="form"
    @submit="onFormSubmit"
  >
    <v-row align="stretch">
      <v-col cols="10">
        <div>
          <v-textarea
            v-model="replyContent"
            class="text-body-1"
            name="content"
            rows="7"
            :placeholder="$t('service_respond')"
            solo
            hide-details
          />
        </div>
        <div
          v-show="attachmentsShow"
          ref="attachments"
          class="mt-3"
        />
      </v-col>
      <v-col class="d-flex flex-column justify-space-between">
        <div class="mb-2">
          <v-btn
            small
            dark
            color="green"
            :disabled="isAttachmentsAdditionDisabled"
            @click="onAttachmentAdd"
          >
            <v-icon
              left
              small
            >
              mdi-paperclip
            </v-icon>
            {{ $t('actions.add_attachment') }}
          </v-btn>
        </div>
        <div>
          <v-checkbox
            v-model="close"
            dense
            hide-details
            name="close"
          >
            <template #label>
              <span class="ml-2 text-body-1">{{ $t('service_close') }}</span>
            </template>
          </v-checkbox>
          <btn-send
            class="mt-2"
            :disabled="loader"
            @click="onFormSubmit"
          />
        </div>
      </v-col>
    </v-row>
  </v-form>
</template>

<script>
import BtnSend from '@components/common/button/BtnSend.vue';
import AddAttachmentMixin from '@components/common/mixins/AddAttachmentMixin.vue';
import { mapGetters } from 'vuex';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  name: 'ErrorReportReplyForm',
  components: { BtnSend },
  mixins: [
    AddAttachmentMixin,
    SnackbarMixin,
  ],
  props: {
    id: {
      type: Number,
      required: true,
    },
    isReady: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loader: false,
      replyContent: '',
      close: this.isReady,
      attachments: [],
      attachmentProps: {
        dense: true,
        hideDetails: true,
        inputClass: 'text-caption',
        wrapperClass: 'd-flex align-center text-caption py-2',
      },
    };
  },
  computed: {
    ...mapGetters({
      getUser: 'auth/getUser',
    }),
  },
  methods: {
    onFormSubmit() {
      if (this.$refs.form.validate()) {
        this.loader = true;
        this.$emit('send', {
          event: 'message',
          comment: this.replyContent,
          user: this.getUser.username,
          loading: true,
        });

        const formData = new FormData();
        formData.append('id', this.id);
        formData.append('description', this.replyContent);
        formData.append('close', this.close);

        this.appendFilesToForm(formData);

        this.axios({
          method: 'POST',
          url: 'cm/report_error/post',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
          .then(() => {
            this.showSnackbar(
              'success',
              this.$t('service_replySent'),
            );
            // this.$emit('success', response.data);
          })
          .catch(() => {
            this.showSnackbar(
              'error',
              this.$t('service_replySentProblem'),
            );
            // this.$emit('failure');
          })
          .finally(() => {
            this.loader = false;
          });
      }
    },
  },
};
</script>
