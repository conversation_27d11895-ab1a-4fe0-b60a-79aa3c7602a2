<template>
  <v-snackbar
    v-model="show"
    :color="color"
    style="z-index: 1400"
    height="100px"
    max-width="70%"
  >
    <v-row
      align="center"
      justify="space-between"
    >
      <v-col
        class="text-left"
        cols="8"
      >
        <h2>{{ message }}</h2>
      </v-col>
      <v-col
        cols="4"
        class="text-right"
      >
        <v-btn
          text
          outlined
          @click="show = false"
        >
          {{ $t('actions.close') }}
        </v-btn>
      </v-col>
    </v-row>
  </v-snackbar>
</template>

<script>
export default {
  name: 'SnackbarMessage',
  data() {
    return {
      show: false,
      message: '',
      color: '',
    };
  },
  created() {
    this.$store.subscribe((mutation, state) => {
      if (mutation.type === 'snackbar/showMessage') {
        this.message = state.snackbar.content;
        this.color = state.snackbar.color;
        this.show = true;
      }
    });
  },
};
</script>
