<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-view-dashboard</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        Dashboard
      </v-toolbar-title>
    </v-toolbar>
    <v-card-text class="pa-0 mt-0">
      <v-tabs
        v-model="activeTab"
        dens
        dark
        background-color="secondary lighten-1"
      >
        <v-tab key="issues">
          Zgłoszenia
        </v-tab>
        <v-tab key="visit_planning">
          Planowanie wizyt
        </v-tab>
        <v-tab key="visits">
          Wizyty
        </v-tab>
      </v-tabs>
      <v-tabs-items v-model="activeTab">
        <v-tab-item
          key="issues"
          class="pr-4 pl-4"
        >
          <IssuesTab />
        </v-tab-item>
        <v-tab-item
          key="visit_planning"
          class="pa-4"
        >
          <VisitPlanningTab />
        </v-tab-item>
        <v-tab-item
          key="visits"
          class="pa-4"
        >
          <VisitsTab />
        </v-tab-item>
      </v-tabs-items>
    </v-card-text>
  </v-card>
</template>

<script>
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import IssuesTab from './dashboard/issues/IssuesTab.vue';
import VisitPlanningTab from './dashboard/VisitPlanningTab.vue';
import VisitsTab from './dashboard/VisitsTab.vue';

export default {
  components: {
    IssuesTab,
    VisitPlanningTab,
    VisitsTab,
  },
  mixins: [
    SnackbarMixin,
  ],
  data() {
    return {
      activeTab: 0,
    };
  },
};
</script>

<style scoped>
/* Add your styles here */
</style>
