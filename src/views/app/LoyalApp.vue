<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-cellphone-wireless</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_loyalAppManager') }}
      </v-toolbar-title>
      <v-spacer />
      <div style="width: 300px">
        <v-select
          v-model="currentApp"
          class="pt-6"
          item-text="name"
          item-value="name"
          :loading="loader"
          :items="apps"
          :label="$t('common_loyalAppManager')"
          dense
          small
          outlined
        />
      </div>
    </v-toolbar>
    <v-card-text class="pa-0 mt-0">
      <v-tabs
        dens
        dark
        background-color="secondary lighten-1"
        show-arrows
      >
        <v-tab
          v-for="(item) in tabsContent"
          :key="item.key"
          ripple
        >
          {{ item.text }}
        </v-tab>
        <v-tab-item
          v-for="(item) in tabsContent"
          :key="item.key"
          class="pa-4"
        >
          <component
            :is="item.component"
            v-bind="{
              ...item.props,
              app: currentApp,
            }"
          />
        </v-tab-item>
      </v-tabs>
    </v-card-text>
  </v-card>
</template>

<script>
import LoyalAppUsers from '@components/loyal-app/users/LoyalAppUsersList.vue';
import LoyalAppPromotionalCodes from '@components/loyal-app/codes/LoyalAppPromotionalCodes.vue';
import LoyalAppInvoices from '@components/loyal-app/invoices/LoyalAppInvoices.vue';
import LoyalAppPromotionalPackages from '@components/loyal-app/packages/LoyalAppPromotionalPackages.vue';
import LoyalAppSelfInvoices from '@components/loyal-app/self-invoice/LoyalAppSelfInvoices.vue';
import LoyalAppAlerts from '@components/loyal-app/alerts/LoyalAppAlerts.vue';
import LoyalAppReceipts from '@components/loyal-app/recipet/LoyalAppReceipts.vue';
import LoyalAppCarwashes from '@components/loyal-app/carwashes/LoyalAppCarwashes.vue';
import LoyalAppTransactionsList from '@components/loyal-app/transactions/LoyalAppTransactionsList.vue';
import LoyalAppUserAppPaymentsList from '@components/loyal-app/payments/LoyalAppUserAppPaymentsList.vue';
import LoyalAppReports from '@components/loyal-app/reports/LoyalAppReports.vue';
import LoyalAppSubscriptionPackages
  from '@components/loyal-app/subscription-packages/LoyalAppSubscriptionPackages.vue';
import LoyalAppStatistics from '@components/loyal-app/statistics/LoyalAppStatistics.vue';

export default {
  components: {
    LoyalAppUsers,
    LoyalAppPromotionalCodes,
    LoyalAppInvoices,
    LoyalAppPromotionalPackages,
    LoyalAppReceipts,
    LoyalAppCarwashes,
    LoyalAppTransactionsList,
    LoyalAppUserAppPaymentsList,
    LoyalAppSelfInvoices,
    LoyalAppAlerts,
    LoyalAppReports,
    LoyalAppSubscriptionPackages,
    LoyalAppStatistics,
  },
  data() {
    return {
      loader: true,
      currentApp: null,
      apps: [
        'BE_LOYAL',
        'WASHSTOP',
      ],
      logout: false,
      applicationError: false,
      tabsContent: [
        {
          text: this.$t('loyalApp_stats'),
          component: 'loyal-app-statistics',
          key: 'statistics',
          props: {
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('common_carwashes'),
          component: 'loyal-app-carwashes',
          key: 'carwashes',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('loyalApp_usersList'),
          component: 'loyal-app-users',
          key: 'user',
          props: {
            showFiltering: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('loyalApp_promotionalCodes'),
          component: 'loyal-app-promotional-codes',
          key: 'promotional-codes',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('common_invoices'),
          component: 'loyal-app-invoices',
          key: 'invoices',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('loyalApp_receipts'),
          component: 'loyal-app-receipts',
          key: 'receipts',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('loyalApp_promotionalPackages'),
          component: 'loyal-app-promotional-packages',
          key: 'promotional-packages',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('loyal-app-manager.transactions'),
          component: 'loyal-app-transactions-list',
          key: 'transactions',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('loyalApp_payments'),
          component: 'loyal-app-user-app-payments-list',
          key: 'payments-list',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('loyalApp_selfInvoices'),
          component: 'loyal-app-self-invoices',
          key: 'self-invoices',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('loyalApp_loyalappUserAlert'),
          component: 'loyal-app-alerts',
          key: 'alertss',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('loyalApp_Reports'),
          component: 'loyal-app-reports',
          key: 'reports',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
        {
          text: this.$t('loyalApp_subscriptions'),
          component: 'loyal-app-subscription-packages',
          key: 'subscription-packages',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
          },
        },
      ],
    };
  },
  mounted() {
    this.getLoyalApps();
  },
  methods: {
    getLoyalApps() {
      this.axios.get(
        '/api/loyalapp/apps',
      )
        .then((response) => {
          this.apps = response.data;
          this.currentApp = response.data[0].name ?? null;
          this.loader = false;
        });
    },
  },
};
</script>
