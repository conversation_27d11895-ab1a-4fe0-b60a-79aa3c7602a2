<template>
  <div>
    <v-card>
      <v-toolbar
        color="secondary"
        dark
        flat
      >
        <v-btn
          icon
          dark
        >
          <v-icon>mdi-account-multiple</v-icon>
        </v-btn>

        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          {{ $t('admin_subscribersHeading') }}
        </v-toolbar-title>
        <!--        <v-spacer />-->
        <!--        <v-tooltip bottom>-->
        <!--          <template #activator="{ on, attrs }">-->
        <!--            <v-btn-->
        <!--              icon-->
        <!--              dark-->
        <!--              v-bind="attrs"-->
        <!--              @click="openModal"-->
        <!--              v-on="on"-->
        <!--            >-->
        <!--              <v-icon>mdi-plus</v-icon>-->
        <!--            </v-btn>-->
        <!--          </template>-->
        <!--          <span>{{ $t('actions.add_user') }}</span>-->
        <!--        </v-tooltip>-->
      </v-toolbar>
      <v-card-text>
        <subscribers-list />
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import SubscribersList from '@components/admin/SubscribersList.vue';

export default {
  name: 'SubscribersView',
  components: {
    SubscribersList,
  },
};
</script>
